# 数据源配置
spring:
  autoconfigure:
    # 排除 Druid 自动配置
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *************************************************************************************************************************
          username: root
          password: mysql@jdy1999123JDY
          druid:
            # 初始连接数
            initial-size: 10
            # 最小连接池数量
            min-idle: 10
            # 最大连接池数量
            max-active: 50
            # 获取连接等待超时时间
            max-wait: 60000
            # 检测间隔时间
            time-between-eviction-runs-millis: 60000
            # 连接最小生存时间
            min-evictable-idle-time-millis: 300000
            # 连接最大生存时间
            max-evictable-idle-time-millis: 900000
            # 检测连接是否有效
            validation-query: SELECT 1
            # 申请连接时检测
            test-on-borrow: false
            # 归还连接时检测
            test-on-return: false
            # 空闲时检测
            test-while-idle: true
            # 是否缓存preparedStatement
            pool-prepared-statements: true
            # 缓存preparedStatement的最大数量
            max-pool-prepared-statement-per-connection-size: 20
            # 配置监控统计拦截的filters
            filters: stat,wall,log4j2
            # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
            connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000