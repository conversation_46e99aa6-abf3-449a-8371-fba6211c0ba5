const { defineConfig } = require('@vue/cli-service')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const path = require('path')

// 环境变量
const isProd = process.env.NODE_ENV === 'production'
const isTest = process.env.NODE_ENV === 'test'
const isDev = process.env.NODE_ENV === 'development'

// 应用配置
const APP_CONFIG = {
  title: '山海小说爬虫系统',
  version: '2.0.0',
  description: '基于Vue.js的现代化管理界面'
}

module.exports = defineConfig({
  // 基础配置
  publicPath: isProd ? './' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  indexPath: 'index.html',

  // 开发配置
  transpileDependencies: true,
  lintOnSave: isDev ? 'warning' : false,
  productionSourceMap: !isProd,

  // 开发服务器配置
  devServer: {
    port: process.env.PORT || 8082,
    open: true,
    hot: true,
    compress: true,
    historyApiFallback: true,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8200',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        pathRewrite: {
          '^/api': '/api'
        }
      }
    },
    // 开发环境下的错误覆盖
    client: {
      overlay: {
        errors: true,
        warnings: false
      }
    }
  },
  configureWebpack: config => {
    if (isProd) {
      config.plugins.push(
        new CompressionWebpackPlugin({
          test: /\.(js|css|html|svg)$/,
          threshold: 10240,
          minRatio: 0.8
        })
      );
      config.optimization = {
        splitChunks: {
          chunks: 'all',
          minSize: 30000,
          maxSize: 500000,
          minChunks: 1,
          automaticNameDelimiter: '-',
          cacheGroups: {
            vendors: {
              test: /[\\/]node_modules[\\/]/,
              name: 'chunk-vendors',
              priority: -10,
              chunks: 'initial'
            },
            common: {
              name: 'chunk-common',
              minChunks: 2,
              priority: -20,
              chunks: 'initial',
              reuseExistingChunk: true
            }
          }
        }
      }
    }
  }
}) 