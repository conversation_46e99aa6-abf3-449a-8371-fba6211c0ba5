<template>
  <div>
    <el-table :data="userList" style="width: 100%" border stripe class="user-table"
      :loading="tableLoading"
      row-class-name="custom-row"
      empty-text="暂无数据，请点击新增用户"
      @selection-change="$emit('update:multipleSelection', $event)">
      <el-table-column type="selection" width="55" fixed="left" align="center" />
      <el-table-column
        v-for="col in columns"
        v-if="col.visible"
        :key="col.prop"
        :prop="col.prop"
        :label="col.label"
        :width="col.width"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div v-if="col.prop === 'status'">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'"
              :style="scope.row.status === '1' ? 'background: var(--success); color: #fff; border: none;' : 'background: var(--danger); color: #fff; border: none;'">
              {{ scope.row.status === '1' ? '启用' : '禁用' }}
            </el-tag>
          </div>
          <span v-else>{{ scope.row[col.prop] }}</span>
        </template>
      </el-table-column>
      <!-- 操作列始终显示，单独处理 -->
      <el-table-column
        label="操作"
        width="220"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="$emit('edit', scope.row)">编辑</el-button>
          <el-button size="mini" type="text" @click="$emit('toggle-status', scope.row)">
            {{ scope.row.status === '1' ? '禁用' : '启用' }}
          </el-button>
          <el-button size="mini" type="text" @click="$emit('delete', scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="table-footer-bar">
      <el-pagination @size-change="$emit('size-change', $event)" @current-change="$emit('current-change', $event)"
        :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" class="user-pagination" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'UserTable',
  props: {
    userList: { type: Array, required: true },
    columns: { type: Array, required: true },
    tableLoading: { type: Boolean, default: false },
    multipleSelection: { type: Array, default: () => [] },
    pagination: { type: Object, required: true }
  }
}
</script>
<style scoped>
.table-footer-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 0 8px 4px 0;
  background: #fff;
  border-radius: 0 0 10px 10px;
}
.user-pagination {
  margin: 0;
  padding: 0;
  background: transparent;
}
</style> 