<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shanhai</groupId>
    <artifactId>shanhai</artifactId>
    <version>2.0.0</version>
    <name>shanhai</name>
    <description>山海小说爬虫系统 - 基于DDD架构的现代化爬虫平台</description>
    <packaging>pom</packaging>
    <url>https://github.com/shanhai/shanhai-novel-crawler</url>

    <properties>
        <!-- Java版本 -->
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- Spring Boot版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <spring-security.version>5.8.10</spring-security.version>

        <!-- 数据库相关版本 -->
        <mysql.version>8.0.35</mysql.version>
        <druid.version>1.2.21</druid.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <dynamic-datasource.version>4.2.0</dynamic-datasource.version>
        <pagehelper.version>1.4.7</pagehelper.version>
        <mybatis-plus-ext.version>3.5.7-EXT695</mybatis-plus-ext.version>

        <!-- 工具类版本 -->
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.25</hutool.version>
        <jsoup.version>1.17.2</jsoup.version>
        <jackson.version>2.15.4</jackson.version>

        <!-- 测试相关版本 -->
        <junit.version>5.10.1</junit.version>
        <mockito.version>5.8.0</mockito.version>

        <!-- 插件版本 -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-failsafe-plugin.version>3.2.2</maven-failsafe-plugin.version>
    </properties>

    <!-- 子模块 -->
    <modules>
        <module>shanhai-api</module>
        <module>shanhai-common</module>
        <module>shanhai-framework</module>
        <module>shanhai-service</module>
    </modules>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.shanhai</groupId>
                <artifactId>shanhai-framework</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shanhai</groupId>
                <artifactId>shanhai-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shanhai</groupId>
                <artifactId>shanhai-common-crawler</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shanhai</groupId>
                <artifactId>shanhai-service-rule</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 数据库相关 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tangzc</groupId>
                <artifactId>mybatis-plus-ext-spring-boot-starter</artifactId>
                <version>${mybatis-plus-ext.version}</version>
            </dependency>

            <!-- 工具类 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- 测试相关 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <outputDirectory>${project.build.directory}/classes</outputDirectory>
        <testOutputDirectory>${project.build.directory}/test-classes</testOutputDirectory>
        <resources>
            <resource>
                <!-- 指定资源文件位置 -->
                <directory>src/main/resources</directory>
                <!-- 处理文件时，需要对文件进行变量替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <profiles>
        <!-- 测试环境 -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

</project>