package com.shanhai.common.crawler.domain.entity.rule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 反爬虫配置实体
 * <p>
 * 定义反爬虫策略和参数配置，用于应对各种反爬虫机制。
 * 包括请求频率控制、代理设置、用户代理轮换等功能。
 * 
 * <p>主要功能：
 * <ul>
 *   <li>请求频率控制和延时策略</li>
 *   <li>代理服务器管理和轮换</li>
 *   <li>用户代理字符串轮换</li>
 *   <li>验证码处理和重试机制</li>
 * </ul>
 * 
 * <p>支持的反爬虫策略：
 * <ul>
 *   <li>时间策略：随机延时、固定间隔</li>
 *   <li>身份策略：User-Agent轮换、请求头伪装</li>
 *   <li>网络策略：代理轮换、IP池管理</li>
 *   <li>行为策略：模拟人类行为、随机访问</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_anti_spider", comment = "反爬虫配置表")
@TableName("crawler_rule_anti_spider")
public class AntiSpiderConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 关联的爬虫规则ID
     */
    @Column(value = "rule_id", comment = "关联的爬虫规则ID", length = 32)
    private String ruleId;

    /**
     * 是否启用反爬虫配置
     */
    @Builder.Default
    @Column(value = "enabled", comment = "是否启用反爬虫配置")
    private Boolean enabled = true;

    // ==================== 延时策略配置 ====================

    /**
     * 最小延时时间（毫秒）
     * 请求之间的最小间隔时间
     */
    @Builder.Default
    @Min(value = 100, message = "最小延时不能小于100毫秒")
    @Column(value = "min_delay_millis", comment = "最小延时时间(毫秒)")
    private Integer minDelayMillis = 1000;

    /**
     * 最大延时时间（毫秒）
     * 请求之间的最大间隔时间
     */
    @Builder.Default
    @Min(value = 500, message = "最大延时不能小于500毫秒")
    @Column(value = "max_delay_millis", comment = "最大延时时间(毫秒)")
    private Integer maxDelayMillis = 3000;

    /**
     * 延时策略
     * 延时计算策略：FIXED（固定）、RANDOM（随机）、EXPONENTIAL（指数退避）
     */
    @Builder.Default
    @Column(value = "delay_strategy", comment = "延时策略", length = 20)
    private String delayStrategy = "RANDOM";

    /**
     * 指数退避基数
     * 当使用指数退避策略时的基数
     */
    @Builder.Default
    @Column(value = "exponential_base", comment = "指数退避基数")
    private Double exponentialBase = 2.0;

    /**
     * 指数退避最大次数
     * 指数退避的最大重试次数
     */
    @Builder.Default
    @Column(value = "exponential_max_attempts", comment = "指数退避最大次数")
    private Integer exponentialMaxAttempts = 5;

    // ==================== 重试策略配置 ====================

    /**
     * 最大重试次数
     * 请求失败时的最大重试次数
     */
    @Builder.Default
    @Min(value = 0, message = "重试次数不能小于0")
    @Max(value = 10, message = "重试次数不能大于10")
    @Column(value = "max_retry_count", comment = "最大重试次数")
    private Integer maxRetryCount = 3;

    /**
     * 重试延时时间（毫秒）
     * 重试之间的延时时间
     */
    @Builder.Default
    @Column(value = "retry_delay_millis", comment = "重试延时时间(毫秒)")
    private Integer retryDelayMillis = 5000;

    /**
     * 重试条件
     * 触发重试的HTTP状态码列表
     */
    @Column(value = "retry_conditions", comment = "重试条件(JSON数组)", type = "TEXT")
    private List<Integer> retryConditions;

    // ==================== 超时配置 ====================

    /**
     * 连接超时时间（毫秒）
     * 建立连接的超时时间
     */
    @Builder.Default
    @Column(value = "connect_timeout_millis", comment = "连接超时时间(毫秒)")
    private Integer connectTimeoutMillis = 10000;

    /**
     * 读取超时时间（毫秒）
     * 读取响应的超时时间
     */
    @Builder.Default
    @Column(value = "read_timeout_millis", comment = "读取超时时间(毫秒)")
    private Integer readTimeoutMillis = 30000;

    /**
     * 总超时时间（毫秒）
     * 整个请求的总超时时间
     */
    @Builder.Default
    @Column(value = "total_timeout_millis", comment = "总超时时间(毫秒)")
    private Integer totalTimeoutMillis = 60000;

    // ==================== User-Agent配置 ====================

    /**
     * 是否随机User-Agent
     * 是否使用随机的User-Agent字符串
     */
    @Builder.Default
    @Column(value = "random_user_agent", comment = "是否随机User-Agent")
    private Boolean randomUserAgent = true;

    /**
     * User-Agent列表
     * 可用的User-Agent字符串列表
     */
    @Column(value = "user_agent_list", comment = "User-Agent列表(JSON数组)", type = "TEXT")
    private List<String> userAgentList;

    /**
     * 默认User-Agent
     * 当不使用随机User-Agent时的默认值
     */
    @Column(value = "default_user_agent", comment = "默认User-Agent", length = 500)
    private String defaultUserAgent;

    // ==================== 代理配置 ====================

    /**
     * 是否启用代理
     * 是否使用代理服务器
     */
    @Builder.Default
    @Column(value = "proxy_enabled", comment = "是否启用代理")
    private Boolean proxyEnabled = false;

    /**
     * 代理服务器列表
     * 可用的代理服务器列表（格式：host:port）
     */
    @Column(value = "proxy_list", comment = "代理服务器列表(JSON数组)", type = "TEXT")
    private List<String> proxyList;

    /**
     * 代理轮换策略
     * 代理服务器的轮换策略：ROUND_ROBIN（轮询）、RANDOM（随机）、STICKY（粘性）
     */
    @Builder.Default
    @Column(value = "proxy_rotation_strategy", comment = "代理轮换策略", length = 20)
    private String proxyRotationStrategy = "ROUND_ROBIN";

    /**
     * 代理验证
     * 是否验证代理服务器的可用性
     */
    @Builder.Default
    @Column(value = "proxy_validation_enabled", comment = "是否启用代理验证")
    private Boolean proxyValidationEnabled = true;

    /**
     * 代理验证超时时间（毫秒）
     * 验证代理服务器时的超时时间
     */
    @Builder.Default
    @Column(value = "proxy_validation_timeout_millis", comment = "代理验证超时时间(毫秒)")
    private Integer proxyValidationTimeoutMillis = 5000;

    // ==================== Cookie配置 ====================

    /**
     * 是否启用Cookie
     * 是否自动处理Cookie
     */
    @Builder.Default
    @Column(value = "cookie_enabled", comment = "是否启用Cookie")
    private Boolean cookieEnabled = true;

    /**
     * Cookie存储策略
     * Cookie的存储策略：MEMORY（内存）、FILE（文件）、DATABASE（数据库）
     */
    @Builder.Default
    @Column(value = "cookie_storage_strategy", comment = "Cookie存储策略", length = 20)
    private String cookieStorageStrategy = "MEMORY";

    /**
     * 自定义Cookie
     * 预设的Cookie值
     */
    @Column(value = "custom_cookies", comment = "自定义Cookie(JSON格式)", type = "TEXT")
    private Map<String, String> customCookies;

    // ==================== 请求头配置 ====================

    /**
     * 自定义请求头
     * 额外的HTTP请求头
     */
    @Column(value = "custom_headers", comment = "自定义请求头(JSON格式)", type = "TEXT")
    private Map<String, String> customHeaders;

    /**
     * 是否随机请求头
     * 是否随机化某些请求头的值
     */
    @Builder.Default
    @Column(value = "random_headers", comment = "是否随机请求头")
    private Boolean randomHeaders = false;

    /**
     * 请求头模板
     * 请求头的模板配置
     */
    @Column(value = "header_templates", comment = "请求头模板(JSON格式)", type = "TEXT")
    private Map<String, List<String>> headerTemplates;

    // ==================== 验证码处理配置 ====================

    /**
     * 验证码处理策略
     * 遇到验证码时的处理策略：SKIP（跳过）、RETRY（重试）、MANUAL（人工）、OCR（识别）
     */
    @Builder.Default
    @Column(value = "captcha_strategy", comment = "验证码处理策略", length = 20)
    private String captchaStrategy = "RETRY";

    /**
     * 验证码检测选择器
     * 用于检测页面是否包含验证码的选择器
     */
    @Column(value = "captcha_detection_selector", comment = "验证码检测选择器", length = 200)
    private String captchaDetectionSelector;

    /**
     * 验证码处理等待时间（毫秒）
     * 遇到验证码时的等待时间
     */
    @Builder.Default
    @Column(value = "captcha_wait_millis", comment = "验证码处理等待时间(毫秒)")
    private Integer captchaWaitMillis = 30000;

    // ==================== 行为模拟配置 ====================

    /**
     * 是否启用JavaScript渲染
     * 是否使用浏览器引擎渲染JavaScript
     */
    @Builder.Default
    @Column(value = "javascript_enabled", comment = "是否启用JavaScript渲染")
    private Boolean javascriptEnabled = false;

    /**
     * 页面加载等待时间（毫秒）
     * 等待页面完全加载的时间
     */
    @Builder.Default
    @Column(value = "page_load_wait_millis", comment = "页面加载等待时间(毫秒)")
    private Integer pageLoadWaitMillis = 3000;

    /**
     * 是否模拟鼠标行为
     * 是否模拟鼠标移动、点击等行为
     */
    @Builder.Default
    @Column(value = "mouse_simulation_enabled", comment = "是否模拟鼠标行为")
    private Boolean mouseSimulationEnabled = false;

    /**
     * 是否模拟键盘行为
     * 是否模拟键盘输入行为
     */
    @Builder.Default
    @Column(value = "keyboard_simulation_enabled", comment = "是否模拟键盘行为")
    private Boolean keyboardSimulationEnabled = false;

    // ==================== 业务方法 ====================

    /**
     * 检查反爬虫配置是否启用
     * 
     * @return 是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(enabled);
    }

    /**
     * 获取有效的最小延时时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最小延时时间（毫秒）
     */
    public int getEffectiveMinDelayMillis() {
        if (minDelayMillis == null || minDelayMillis < 100) {
            return 1000; // 默认1秒
        }
        return minDelayMillis;
    }

    /**
     * 获取有效的最大延时时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最大延时时间（毫秒）
     */
    public int getEffectiveMaxDelayMillis() {
        if (maxDelayMillis == null || maxDelayMillis < getEffectiveMinDelayMillis()) {
            return Math.max(3000, getEffectiveMinDelayMillis() + 1000); // 默认3秒或最小延时+1秒
        }
        return maxDelayMillis;
    }

    /**
     * 获取有效的重试次数
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 重试次数
     */
    public int getEffectiveMaxRetryCount() {
        if (maxRetryCount == null || maxRetryCount < 0) {
            return 3; // 默认3次
        }
        if (maxRetryCount > 10) {
            return 10; // 最大10次
        }
        return maxRetryCount;
    }

    /**
     * 检查是否启用了代理
     * 
     * @return 是否启用代理
     */
    public boolean isProxyEnabled() {
        return Boolean.TRUE.equals(proxyEnabled) && proxyList != null && !proxyList.isEmpty();
    }

    /**
     * 检查是否启用了随机User-Agent
     * 
     * @return 是否启用随机User-Agent
     */
    public boolean isRandomUserAgentEnabled() {
        return Boolean.TRUE.equals(randomUserAgent) && userAgentList != null && !userAgentList.isEmpty();
    }

    /**
     * 检查是否为固定延时策略
     * 
     * @return 是否为固定延时策略
     */
    public boolean isFixedDelayStrategy() {
        return "FIXED".equalsIgnoreCase(delayStrategy);
    }

    /**
     * 检查是否为随机延时策略
     * 
     * @return 是否为随机延时策略
     */
    public boolean isRandomDelayStrategy() {
        return "RANDOM".equalsIgnoreCase(delayStrategy);
    }

    /**
     * 检查是否为指数退避延时策略
     * 
     * @return 是否为指数退避延时策略
     */
    public boolean isExponentialDelayStrategy() {
        return "EXPONENTIAL".equalsIgnoreCase(delayStrategy);
    }

    /**
     * 检查是否为轮询代理策略
     * 
     * @return 是否为轮询代理策略
     */
    public boolean isRoundRobinProxyStrategy() {
        return "ROUND_ROBIN".equalsIgnoreCase(proxyRotationStrategy);
    }

    /**
     * 检查是否为随机代理策略
     * 
     * @return 是否为随机代理策略
     */
    public boolean isRandomProxyStrategy() {
        return "RANDOM".equalsIgnoreCase(proxyRotationStrategy);
    }

    /**
     * 检查是否为粘性代理策略
     * 
     * @return 是否为粘性代理策略
     */
    public boolean isStickyProxyStrategy() {
        return "STICKY".equalsIgnoreCase(proxyRotationStrategy);
    }

    /**
     * 检查是否需要验证码处理
     * 
     * @return 是否需要验证码处理
     */
    public boolean needsCaptchaHandling() {
        return captchaDetectionSelector != null && !captchaDetectionSelector.trim().isEmpty();
    }

    /**
     * 检查是否启用了行为模拟
     * 
     * @return 是否启用行为模拟
     */
    public boolean isBehaviorSimulationEnabled() {
        return Boolean.TRUE.equals(mouseSimulationEnabled) || Boolean.TRUE.equals(keyboardSimulationEnabled);
    }

    /**
     * 计算延时时间
     * 根据延时策略计算实际的延时时间
     * 
     * @param attemptCount 尝试次数（用于指数退避）
     * @return 延时时间（毫秒）
     */
    public long calculateDelayMillis(int attemptCount) {
        int minDelay = getEffectiveMinDelayMillis();
        int maxDelay = getEffectiveMaxDelayMillis();
        
        switch (delayStrategy.toUpperCase()) {
            case "FIXED":
                return minDelay;
            case "RANDOM":
                return minDelay + (long) (Math.random() * (maxDelay - minDelay));
            case "EXPONENTIAL":
                if (exponentialBase == null || exponentialBase <= 1.0) {
                    exponentialBase = 2.0;
                }
                long exponentialDelay = (long) (minDelay * Math.pow(exponentialBase, Math.min(attemptCount, exponentialMaxAttempts)));
                return Math.min(exponentialDelay, maxDelay);
            default:
                return minDelay + (long) (Math.random() * (maxDelay - minDelay));
        }
    }

    /**
     * 检查HTTP状态码是否需要重试
     * 
     * @param statusCode HTTP状态码
     * @return 是否需要重试
     */
    public boolean shouldRetry(int statusCode) {
        if (retryConditions == null || retryConditions.isEmpty()) {
            // 默认重试条件：5xx服务器错误和部分4xx客户端错误
            return statusCode >= 500 || statusCode == 429 || statusCode == 408;
        }
        return retryConditions.contains(statusCode);
    }
}
