package com.shanhai.api.controller.validator;

import com.shanhai.api.controller.dto.NovelCrawlerDTO;
import org.springframework.stereotype.Component;

/**
 * 小说爬虫请求验证器
 * 负责验证所有爬虫相关的请求参数
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
public class NovelCrawlerValidator {

    /**
     * 验证搜索请求参数
     */
    public void validateSearchRequest(NovelCrawlerDTO.SearchRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getKeyword() == null || request.getKeyword().trim().isEmpty()) {
            throw new IllegalArgumentException("搜索关键词不能为空");
        }
        if (request.getKeyword().trim().length() < 2) {
            throw new IllegalArgumentException("搜索关键词长度不能少于2个字符");
        }
    }

    /**
     * 验证书籍详情请求参数
     */
    public void validateBookInfoRequest(NovelCrawlerDTO.BookInfoRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getBookUrl() == null || request.getBookUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("书籍URL不能为空");
        }
        if (!request.getBookUrl().startsWith("http")) {
            throw new IllegalArgumentException("书籍URL格式不正确");
        }
    }

    /**
     * 验证章节列表请求参数
     */
    public void validateChapterListRequest(NovelCrawlerDTO.ChapterListRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getChapterListUrl() == null || request.getChapterListUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("章节列表URL不能为空");
        }
        if (!request.getChapterListUrl().startsWith("http")) {
            throw new IllegalArgumentException("章节列表URL格式不正确");
        }
    }

    /**
     * 验证章节内容请求参数
     */
    public void validateChapterContentRequest(NovelCrawlerDTO.ChapterContentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getChapterUrl() == null || request.getChapterUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("章节URL不能为空");
        }
        if (!request.getChapterUrl().startsWith("http")) {
            throw new IllegalArgumentException("章节URL格式不正确");
        }
    }

    /**
     * 验证批量章节内容请求参数
     */
    public void validateBatchChapterContentRequest(NovelCrawlerDTO.BatchChapterContentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getChapterUrls() == null || request.getChapterUrls().isEmpty()) {
            throw new IllegalArgumentException("章节URL列表不能为空");
        }
        if (request.getChapterUrls().size() > 50) {
            throw new IllegalArgumentException("批量获取章节数量不能超过50个");
        }
        for (String url : request.getChapterUrls()) {
            if (url == null || url.trim().isEmpty()) {
                throw new IllegalArgumentException("章节URL不能为空");
            }
            if (!url.startsWith("http")) {
                throw new IllegalArgumentException("章节URL格式不正确: " + url);
            }
        }
    }
} 