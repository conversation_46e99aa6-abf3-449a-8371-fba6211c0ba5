package com.shanhai.common.crawler.domain.entity.rule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.Map;

/**
 * 搜索规则配置实体
 * <p>
 * 定义如何进行书籍搜索的规则配置，包括搜索URL模板、结果解析规则等。
 * 支持多种搜索方式：GET参数、POST表单、API调用等。
 * 
 * <p>主要功能：
 * <ul>
 *   <li>配置搜索URL的构建规则</li>
 *   <li>定义搜索结果的解析规则</li>
 *   <li>支持分页搜索和结果过滤</li>
 *   <li>提供搜索参数的验证和转换</li>
 * </ul>
 * 
 * <p>支持的搜索模式：
 * <ul>
 *   <li>URL参数模式 - 通过URL参数传递搜索关键词</li>
 *   <li>POST表单模式 - 通过POST表单提交搜索请求</li>
 *   <li>API调用模式 - 调用RESTful API进行搜索</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_search", comment = "搜索规则配置表")
@TableName("crawler_rule_search")
public class SearchRuleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 关联的爬虫规则ID
     */
    @Column(value = "rule_id", comment = "关联的爬虫规则ID", length = 32)
    private String ruleId;

    // ==================== 搜索URL配置 ====================

    /**
     * 搜索URL模板
     * 支持占位符：{keyword}、{page}、{pageSize}等
     * 示例：https://example.com/search?q={keyword}&page={page}
     */
    @NotBlank(message = "搜索URL模板不能为空")
    @Column(value = "search_url_template", comment = "搜索URL模板", length = 500)
    private String searchUrlTemplate;

    /**
     * 关键词参数名
     * 在URL中用于传递搜索关键词的参数名
     * 默认为 "keyword" 或 "q"
     */
    @Builder.Default
    @Column(value = "keyword_param_name", comment = "关键词参数名", length = 50)
    private String keywordParamName = "keyword";

    /**
     * 页码参数名
     * 在URL中用于传递页码的参数名
     * 默认为 "page"
     */
    @Builder.Default
    @Column(value = "page_param_name", comment = "页码参数名", length = 50)
    private String pageParamName = "page";

    /**
     * 页面大小参数名
     * 在URL中用于传递每页数量的参数名
     */
    @Column(value = "page_size_param_name", comment = "页面大小参数名", length = 50)
    private String pageSizeParamName;

    // ==================== 请求配置 ====================

    /**
     * HTTP请求方法
     * 支持GET、POST等
     */
    @Builder.Default
    @Column(value = "http_method", comment = "HTTP请求方法", length = 10)
    private String httpMethod = "GET";

    /**
     * 请求头配置
     * JSON格式存储自定义请求头
     */
    @Column(value = "request_headers", comment = "请求头配置(JSON格式)", type = "TEXT")
    private Map<String, String> requestHeaders;

    /**
     * POST请求体模板
     * 当使用POST方法时的请求体模板
     * 支持JSON、表单等格式
     */
    @Column(value = "request_body_template", comment = "POST请求体模板", type = "TEXT")
    private String requestBodyTemplate;

    /**
     * 内容类型
     * 请求的Content-Type
     */
    @Builder.Default
    @Column(value = "content_type", comment = "内容类型", length = 100)
    private String contentType = "application/x-www-form-urlencoded";

    // ==================== 结果解析配置 ====================

    /**
     * 搜索结果列表选择器
     * 用于选择搜索结果列表容器的CSS选择器或JSONPath
     */
    @NotBlank(message = "搜索结果列表选择器不能为空")
    @Column(value = "result_list_selector", comment = "搜索结果列表选择器", length = 200)
    private String resultListSelector;

    /**
     * 单个结果项选择器
     * 用于选择单个搜索结果项的CSS选择器或JSONPath
     */
    @Column(value = "result_item_selector", comment = "单个结果项选择器", length = 200)
    private String resultItemSelector;

    /**
     * 书名选择器
     * 从搜索结果中提取书名的选择器
     */
    @NotBlank(message = "书名选择器不能为空")
    @Column(value = "book_title_selector", comment = "书名选择器", length = 200)
    private String bookTitleSelector;

    /**
     * 作者选择器
     * 从搜索结果中提取作者的选择器
     */
    @Column(value = "author_selector", comment = "作者选择器", length = 200)
    private String authorSelector;

    /**
     * 书籍详情页链接选择器
     * 从搜索结果中提取详情页链接的选择器
     */
    @NotBlank(message = "书籍详情页链接选择器不能为空")
    @Column(value = "book_url_selector", comment = "书籍详情页链接选择器", length = 200)
    private String bookUrlSelector;

    /**
     * 链接属性名
     * 提取链接时使用的HTML属性名，通常为"href"
     */
    @Builder.Default
    @Column(value = "link_attribute_name", comment = "链接属性名", length = 20)
    private String linkAttributeName = "href";

    /**
     * 封面图片选择器
     * 从搜索结果中提取封面图片的选择器
     */
    @Column(value = "cover_image_selector", comment = "封面图片选择器", length = 200)
    private String coverImageSelector;

    /**
     * 图片属性名
     * 提取图片时使用的HTML属性名，通常为"src"
     */
    @Builder.Default
    @Column(value = "image_attribute_name", comment = "图片属性名", length = 20)
    private String imageAttributeName = "src";

    /**
     * 书籍简介选择器
     * 从搜索结果中提取书籍简介的选择器
     */
    @Column(value = "book_intro_selector", comment = "书籍简介选择器", length = 200)
    private String bookIntroSelector;

    /**
     * 书籍状态选择器
     * 从搜索结果中提取书籍状态（连载/完结）的选择器
     */
    @Column(value = "book_status_selector", comment = "书籍状态选择器", length = 200)
    private String bookStatusSelector;

    /**
     * 最新章节选择器
     * 从搜索结果中提取最新章节信息的选择器
     */
    @Column(value = "latest_chapter_selector", comment = "最新章节选择器", length = 200)
    private String latestChapterSelector;

    /**
     * 更新时间选择器
     * 从搜索结果中提取更新时间的选择器
     */
    @Column(value = "update_time_selector", comment = "更新时间选择器", length = 200)
    private String updateTimeSelector;

    // ==================== 分页配置 ====================

    /**
     * 是否支持分页
     */
    @Builder.Default
    @Column(value = "pagination_enabled", comment = "是否支持分页")
    private Boolean paginationEnabled = false;

    /**
     * 起始页码
     * 分页的起始页码，通常为0或1
     */
    @Builder.Default
    @Column(value = "start_page_number", comment = "起始页码")
    private Integer startPageNumber = 1;

    /**
     * 最大页数限制
     * 搜索时的最大页数限制，防止无限制搜索
     */
    @Builder.Default
    @Min(value = 1, message = "最大页数不能小于1")
    @Max(value = 100, message = "最大页数不能大于100")
    @Column(value = "max_page_limit", comment = "最大页数限制")
    private Integer maxPageLimit = 10;

    /**
     * 每页结果数量
     * 每页显示的搜索结果数量
     */
    @Builder.Default
    @Column(value = "page_size", comment = "每页结果数量")
    private Integer pageSize = 20;

    /**
     * 下一页选择器
     * 用于获取下一页链接的选择器
     */
    @Column(value = "next_page_selector", comment = "下一页选择器", length = 200)
    private String nextPageSelector;

    /**
     * 总页数选择器
     * 用于获取总页数的选择器
     */
    @Column(value = "total_pages_selector", comment = "总页数选择器", length = 200)
    private String totalPagesSelector;

    // ==================== 高级配置 ====================

    /**
     * 字符编码
     * 页面内容的字符编码
     */
    @Builder.Default
    @Column(value = "charset", comment = "字符编码", length = 20)
    private String charset = "UTF-8";

    /**
     * 等待加载的选择器
     * 用于动态页面，等待特定元素加载完成
     */
    @Column(value = "wait_for_selector", comment = "等待加载的选择器", length = 200)
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     * 等待页面加载的时间
     */
    @Builder.Default
    @Column(value = "wait_time_millis", comment = "等待时间(毫秒)")
    private Integer waitTimeMillis = 3000;

    /**
     * 结果过滤正则表达式
     * 用于过滤搜索结果的正则表达式
     */
    @Column(value = "result_filter_regex", comment = "结果过滤正则表达式", length = 500)
    private String resultFilterRegex;

    /**
     * 是否去重
     * 是否对搜索结果进行去重处理
     */
    @Builder.Default
    @Column(value = "deduplication_enabled", comment = "是否去重")
    private Boolean deduplicationEnabled = true;

    // ==================== 业务方法 ====================

    /**
     * 验证搜索规则配置的完整性
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        return searchUrlTemplate != null && !searchUrlTemplate.trim().isEmpty()
            && resultListSelector != null && !resultListSelector.trim().isEmpty()
            && bookTitleSelector != null && !bookTitleSelector.trim().isEmpty()
            && bookUrlSelector != null && !bookUrlSelector.trim().isEmpty()
            && httpMethod != null && !httpMethod.trim().isEmpty();
    }

    /**
     * 检查是否为GET请求
     * 
     * @return 是否为GET请求
     */
    public boolean isGetRequest() {
        return "GET".equalsIgnoreCase(httpMethod);
    }

    /**
     * 检查是否为POST请求
     * 
     * @return 是否为POST请求
     */
    public boolean isPostRequest() {
        return "POST".equalsIgnoreCase(httpMethod);
    }

    /**
     * 获取有效的等待时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 等待时间（毫秒）
     */
    public int getEffectiveWaitTimeMillis() {
        if (waitTimeMillis == null || waitTimeMillis <= 0) {
            return 3000; // 默认3秒
        }
        if (waitTimeMillis > 30000) {
            return 30000; // 最大30秒
        }
        return waitTimeMillis;
    }

    /**
     * 获取有效的页面大小
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 页面大小
     */
    public int getEffectivePageSize() {
        if (pageSize == null || pageSize <= 0) {
            return 20; // 默认20条
        }
        if (pageSize > 100) {
            return 100; // 最大100条
        }
        return pageSize;
    }

    /**
     * 获取有效的最大页数限制
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最大页数限制
     */
    public int getEffectiveMaxPageLimit() {
        if (maxPageLimit == null || maxPageLimit <= 0) {
            return 10; // 默认10页
        }
        if (maxPageLimit > 100) {
            return 100; // 最大100页
        }
        return maxPageLimit;
    }

    /**
     * 构建搜索URL
     * 根据模板和参数构建实际的搜索URL
     * 
     * @param keyword 搜索关键词
     * @param pageNumber 页码
     * @return 构建的搜索URL
     */
    public String buildSearchUrl(String keyword, int pageNumber) {
        if (searchUrlTemplate == null || searchUrlTemplate.trim().isEmpty()) {
            return null;
        }
        
        String url = searchUrlTemplate;
        
        // 替换关键词占位符
        if (keyword != null) {
            url = url.replace("{keyword}", keyword)
                     .replace("{" + keywordParamName + "}", keyword);
        }
        
        // 替换页码占位符
        url = url.replace("{page}", String.valueOf(pageNumber))
                 .replace("{" + pageParamName + "}", String.valueOf(pageNumber));
        
        // 替换页面大小占位符
        if (pageSizeParamName != null && !pageSizeParamName.trim().isEmpty()) {
            url = url.replace("{pageSize}", String.valueOf(getEffectivePageSize()))
                     .replace("{" + pageSizeParamName + "}", String.valueOf(getEffectivePageSize()));
        }
        
        return url;
    }

    /**
     * 检查是否需要等待页面加载
     * 
     * @return 是否需要等待
     */
    public boolean needsWaitForLoad() {
        return waitForSelector != null && !waitForSelector.trim().isEmpty();
    }

    /**
     * 检查是否启用了结果过滤
     * 
     * @return 是否启用结果过滤
     */
    public boolean hasResultFilter() {
        return resultFilterRegex != null && !resultFilterRegex.trim().isEmpty();
    }
}
