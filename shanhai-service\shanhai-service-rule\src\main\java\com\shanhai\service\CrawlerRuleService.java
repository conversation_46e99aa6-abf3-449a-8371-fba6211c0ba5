package com.shanhai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;

import java.util.List;

/**
 * 爬虫规则服务接口
 * 
 * 提供爬虫规则的业务逻辑处理，包括：
 * - 规则的增删改查
 * - 规则验证
 * - 规则导入导出
 * - 规则缓存管理
 * 
 * <AUTHOR>
 */
public interface CrawlerRuleService extends IService<CrawlerRuleNovel> {
    
    /**
     * 根据站点名称查询规则
     * 
     * @param sourceName 站点名称
     * @return 爬虫规则
     */
    CrawlerRuleNovel getBySourceName(String sourceName);
    
    /**
     * 验证规则配置是否正确
     * 
     * @param rule 爬虫规则
     * @return 验证结果
     */
    boolean validateRule(CrawlerRuleNovel rule);
    
    /**
     * 批量导入规则
     * 
     * @param rules 规则列表
     * @return 导入成功数量
     */
    int batchImport(List<CrawlerRuleNovel> rules);
    
    /**
     * 导出所有规则
     * 
     * @return 规则列表
     */
    List<CrawlerRuleNovel> exportAll();
    
    /**
     * 启用/禁用规则
     * 
     * @param id 规则ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    boolean toggleRule(String id, boolean enabled);
}
