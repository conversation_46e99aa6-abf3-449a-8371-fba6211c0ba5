package com.shanhai.common.crawler.domain.valueobject;

import lombok.Getter;

/**
 * 规则状态枚举
 * <p>
 * 定义爬虫规则的各种状态，用于控制规则的生命周期和使用权限。
 * 
 * <p>状态说明：
 * <ul>
 *   <li>ENABLED - 启用状态，规则正常工作</li>
 *   <li>DISABLED - 禁用状态，规则暂停使用</li>
 *   <li>MAINTENANCE - 维护状态，规则正在维护中</li>
 *   <li>TESTING - 测试状态，规则正在测试中</li>
 *   <li>DEPRECATED - 已废弃状态，规则即将被移除</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Getter
public enum RuleStatus {

    /**
     * 启用状态
     * 规则正常工作，可以被爬虫引擎使用
     * 
     * <p>特征：
     * <ul>
     *   <li>规则配置完整且有效</li>
     *   <li>可以正常执行爬虫任务</li>
     *   <li>允许被调度系统调用</li>
     * </ul>
     */
    ENABLED("ENABLED", "启用", "规则正常工作，可以被使用", true, true),

    /**
     * 禁用状态
     * 规则暂停使用，不会被爬虫引擎调用
     * 
     * <p>特征：
     * <ul>
     *   <li>规则暂时停止工作</li>
     *   <li>不会被调度系统调用</li>
     *   <li>可以随时重新启用</li>
     * </ul>
     * 
     * <p>常见原因：
     * <ul>
     *   <li>目标网站临时不可访问</li>
     *   <li>规则需要临时调整</li>
     *   <li>管理员手动禁用</li>
     * </ul>
     */
    DISABLED("DISABLED", "禁用", "规则暂停使用，不会被调用", false, true),

    /**
     * 维护状态
     * 规则正在维护中，暂时不可用
     * 
     * <p>特征：
     * <ul>
     *   <li>规则正在进行维护或更新</li>
     *   <li>不会被调度系统调用</li>
     *   <li>维护完成后可恢复正常状态</li>
     * </ul>
     * 
     * <p>常见场景：
     * <ul>
     *   <li>规则配置正在更新</li>
     *   <li>目标网站结构发生变化</li>
     *   <li>需要适配新的反爬虫机制</li>
     * </ul>
     */
    MAINTENANCE("MAINTENANCE", "维护中", "规则正在维护，暂时不可用", false, true),

    /**
     * 测试状态
     * 规则正在测试中，仅限测试环境使用
     * 
     * <p>特征：
     * <ul>
     *   <li>规则处于测试阶段</li>
     *   <li>仅在测试环境中可用</li>
     *   <li>测试通过后可转为启用状态</li>
     * </ul>
     * 
     * <p>适用场景：
     * <ul>
     *   <li>新规则的功能验证</li>
     *   <li>规则更新后的回归测试</li>
     *   <li>性能和稳定性测试</li>
     * </ul>
     */
    TESTING("TESTING", "测试中", "规则正在测试，仅限测试环境", false, true),

    /**
     * 已废弃状态
     * 规则已过时，计划移除
     * 
     * <p>特征：
     * <ul>
     *   <li>规则已不再维护</li>
     *   <li>不建议继续使用</li>
     *   <li>将在未来版本中移除</li>
     * </ul>
     * 
     * <p>常见原因：
     * <ul>
     *   <li>目标网站已关闭</li>
     *   <li>有更好的替代规则</li>
     *   <li>技术架构已过时</li>
     * </ul>
     */
    DEPRECATED("DEPRECATED", "已废弃", "规则已过时，计划移除", false, false);

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 是否可执行
     * 表示在此状态下规则是否可以被执行
     */
    private final boolean executable;

    /**
     * 是否可管理
     * 表示在此状态下规则是否可以被管理（编辑、更新等）
     */
    private final boolean manageable;

    /**
     * 构造函数
     * 
     * @param code 状态代码
     * @param name 状态名称
     * @param description 状态描述
     * @param executable 是否可执行
     * @param manageable 是否可管理
     */
    RuleStatus(String code, String name, String description, boolean executable, boolean manageable) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.executable = executable;
        this.manageable = manageable;
    }

    // ==================== 业务方法 ====================

    /**
     * 根据代码获取规则状态
     * 
     * @param code 状态代码
     * @return 规则状态，如果未找到返回null
     */
    public static RuleStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        String upperCode = code.trim().toUpperCase();
        for (RuleStatus status : values()) {
            if (status.code.equals(upperCode)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据代码获取规则状态，如果未找到返回默认值
     * 
     * @param code 状态代码
     * @param defaultStatus 默认状态
     * @return 规则状态
     */
    public static RuleStatus fromCodeOrDefault(String code, RuleStatus defaultStatus) {
        RuleStatus status = fromCode(code);
        return status != null ? status : defaultStatus;
    }

    /**
     * 检查状态是否可执行
     * 
     * @return 是否可执行
     */
    public boolean isExecutable() {
        return executable;
    }

    /**
     * 检查状态是否可管理
     * 
     * @return 是否可管理
     */
    public boolean isManageable() {
        return manageable;
    }

    /**
     * 检查是否为启用状态
     * 
     * @return 是否为启用状态
     */
    public boolean isEnabled() {
        return this == ENABLED;
    }

    /**
     * 检查是否为禁用状态
     * 
     * @return 是否为禁用状态
     */
    public boolean isDisabled() {
        return this == DISABLED;
    }

    /**
     * 检查是否为维护状态
     * 
     * @return 是否为维护状态
     */
    public boolean isMaintenance() {
        return this == MAINTENANCE;
    }

    /**
     * 检查是否为测试状态
     * 
     * @return 是否为测试状态
     */
    public boolean isTesting() {
        return this == TESTING;
    }

    /**
     * 检查是否为已废弃状态
     * 
     * @return 是否为已废弃状态
     */
    public boolean isDeprecated() {
        return this == DEPRECATED;
    }

    /**
     * 检查是否可以转换到目标状态
     * 
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(RuleStatus targetStatus) {
        if (targetStatus == null || targetStatus == this) {
            return false;
        }

        switch (this) {
            case ENABLED:
                // 启用状态可以转换到任何其他状态
                return true;
            case DISABLED:
                // 禁用状态可以转换到启用、维护、测试、废弃状态
                return targetStatus == ENABLED || targetStatus == MAINTENANCE || 
                       targetStatus == TESTING || targetStatus == DEPRECATED;
            case MAINTENANCE:
                // 维护状态可以转换到启用、禁用、测试、废弃状态
                return targetStatus == ENABLED || targetStatus == DISABLED || 
                       targetStatus == TESTING || targetStatus == DEPRECATED;
            case TESTING:
                // 测试状态可以转换到启用、禁用、维护状态
                return targetStatus == ENABLED || targetStatus == DISABLED || 
                       targetStatus == MAINTENANCE;
            case DEPRECATED:
                // 废弃状态一般不允许转换到其他状态
                return false;
            default:
                return false;
        }
    }

    /**
     * 获取状态的优先级
     * 数值越小优先级越高，用于状态排序
     * 
     * @return 优先级（1-5）
     */
    public int getPriority() {
        switch (this) {
            case ENABLED:
                return 1; // 最高优先级
            case TESTING:
                return 2; // 高优先级
            case MAINTENANCE:
                return 3; // 中等优先级
            case DISABLED:
                return 4; // 低优先级
            case DEPRECATED:
                return 5; // 最低优先级
            default:
                return 3;
        }
    }

    /**
     * 获取状态的颜色代码（用于UI显示）
     * 
     * @return 颜色代码
     */
    public String getColorCode() {
        switch (this) {
            case ENABLED:
                return "#52c41a"; // 绿色
            case DISABLED:
                return "#d9d9d9"; // 灰色
            case MAINTENANCE:
                return "#faad14"; // 橙色
            case TESTING:
                return "#1890ff"; // 蓝色
            case DEPRECATED:
                return "#f5222d"; // 红色
            default:
                return "#d9d9d9"; // 默认灰色
        }
    }

    /**
     * 获取状态的图标名称（用于UI显示）
     * 
     * @return 图标名称
     */
    public String getIconName() {
        switch (this) {
            case ENABLED:
                return "check-circle";
            case DISABLED:
                return "pause-circle";
            case MAINTENANCE:
                return "tool";
            case TESTING:
                return "experiment";
            case DEPRECATED:
                return "exclamation-circle";
            default:
                return "question-circle";
        }
    }

    @Override
    public String toString() {
        return name + "(" + code + ")";
    }
}
