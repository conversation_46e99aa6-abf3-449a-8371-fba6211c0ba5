package com.shanhai.common.crawler.exception;

/**
 * 爬虫服务异常
 * 用于业务层抛出自定义异常，便于全局捕获和处理。
 */
public class ServiceException extends RuntimeException {
    public ServiceException() {
        super();
    }
    public ServiceException(String message) {
        super(message);
    }
    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }
    public ServiceException(Throwable cause) {
        super(cause);
    }
}