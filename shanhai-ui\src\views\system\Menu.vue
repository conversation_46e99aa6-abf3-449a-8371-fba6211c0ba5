<template>
  <div class="menu-page">
    <el-card>
      <div slot="header">
        <span>菜单管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">
          新增菜单
        </el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="菜单名称">
          <el-input v-model="searchForm.menuName" placeholder="请输入菜单名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格 -->
      <el-table 
        :data="menuList" 
        style="width: 100%"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="menuName" label="菜单名称" />
        <el-table-column prop="path" label="路由路径" />
        <el-table-column prop="component" label="组件路径" />
        <el-table-column prop="icon" label="图标" width="100">
          <template slot-scope="scope">
            <i :class="scope.row.icon" v-if="scope.row.icon"></i>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="mini" 
              :type="scope.row.status === '1' ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === '1' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'SystemMenu',
  data() {
    return {
      searchForm: {
        menuName: '',
        status: ''
      },
      menuList: [
        {
          id: 1,
          menuName: '仪表盘',
          path: '/dashboard',
          component: 'Dashboard',
          icon: 'el-icon-s-home',
          sort: 1,
          status: '1',
          createTime: '2024-01-15 10:30'
        },
        {
          id: 2,
          menuName: '内容管理',
          path: '/content',
          component: 'Layout',
          icon: 'el-icon-document',
          sort: 2,
          status: '1',
          createTime: '2024-01-15 10:30',
          children: [
            {
              id: 21,
              menuName: '小说管理',
              path: '/content/novel',
              component: 'content/Novel',
              icon: 'el-icon-reading',
              sort: 1,
              status: '1',
              createTime: '2024-01-15 10:30'
            },
            {
              id: 22,
              menuName: '规则管理',
              path: '/content/rule',
              component: 'content/Rule',
              icon: 'el-icon-setting',
              sort: 2,
              status: '1',
              createTime: '2024-01-15 10:30'
            }
          ]
        },
        {
          id: 3,
          menuName: '系统管理',
          path: '/system',
          component: 'Layout',
          icon: 'el-icon-s-tools',
          sort: 3,
          status: '1',
          createTime: '2024-01-15 10:30',
          children: [
            {
              id: 31,
              menuName: '用户管理',
              path: '/system/user',
              component: 'system/User',
              icon: 'el-icon-user',
              sort: 1,
              status: '1',
              createTime: '2024-01-15 10:30'
            },
            {
              id: 32,
              menuName: '角色管理',
              path: '/system/role',
              component: 'system/Role',
              icon: 'el-icon-s-check',
              sort: 2,
              status: '1',
              createTime: '2024-01-15 10:30'
            },
            {
              id: 33,
              menuName: '菜单管理',
              path: '/system/menu',
              component: 'system/Menu',
              icon: 'el-icon-menu',
              sort: 3,
              status: '1',
              createTime: '2024-01-15 10:30'
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleAdd() {
      this.$message.info('新增菜单功能待实现')
    },
    handleEdit(row) {
      this.$message.info(`编辑菜单：${row.menuName}`)
    },
    handleToggleStatus(row) {
      const newStatus = row.status === '1' ? '0' : '1'
      const statusText = newStatus === '1' ? '启用' : '禁用'
      this.$confirm(`确定要${statusText}菜单"${row.menuName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = newStatus
        this.$message.success(`${statusText}成功`)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除菜单"${row.menuName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSearch() {
      this.$message.info('搜索功能待实现')
    },
    handleReset() {
      this.searchForm = {
        menuName: '',
        status: ''
      }
      this.$message.info('重置成功')
    }
  }
}
</script>

<style scoped>
.menu-page {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style> 