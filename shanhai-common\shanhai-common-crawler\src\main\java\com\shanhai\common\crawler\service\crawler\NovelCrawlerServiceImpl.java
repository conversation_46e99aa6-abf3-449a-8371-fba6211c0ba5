package com.shanhai.common.crawler.service.crawler;

import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.ServiceException;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.strategy.CrawlerStrategy;
import com.shanhai.common.crawler.strategy.CrawlerStrategyFactory;
import com.shanhai.common.crawler.utils.CrawlerNetworkManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 小说爬虫主流程服务实现类
 * <p>
 * 使用策略模式简化流程控制，提高代码可读性和可维护性。
 * 所有参数校验、内容解析均调用工具类。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class NovelCrawlerServiceImpl implements NovelCrawlerService {

    private final CrawlerStrategyFactory strategyFactory;
    private final CrawlerNetworkManager networkManager;

    @Autowired
    public NovelCrawlerServiceImpl(CrawlerStrategyFactory strategyFactory, CrawlerNetworkManager networkManager) {
        this.strategyFactory = strategyFactory;
        this.networkManager = networkManager;
    }

    // ================== 主流程方法 ==================

    /**
     * 校验爬虫规则配置
     *
     * @param config 爬虫规则配置
     */
    private void validateConfig(CrawlerRuleNovel config) {
        if (config == null) {
            throw new ServiceException("爬虫规则配置无效");
        }
    }

    /**
     * 校验字符串参数
     *
     * @param param 参数
     * @param name  参数名
     */
    private void validateString(String param, String name) {
        if (param == null || param.trim().isEmpty()) {
            throw new ServiceException(name + "不能为空");
        }
    }

    /**
     * 搜索小说书籍主流程
     *
     * @param config  爬虫规则配置
     * @param keyword 搜索关键词
     * @return 匹配的书籍列表
     * @throws ServiceException 采集异常
     */
    @Override
    public List<NovelBook> searchBooks(CrawlerRuleNovel config, String keyword) {
        validateConfig(config);
        validateString(keyword, "搜索关键词");
        long start = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString();
        log.info("[{}] 开始搜索书籍，关键词: {}", traceId, keyword);
        try {
            CrawlerStrategy strategy = strategyFactory.getStrategy(config);
            log.debug("[{}] 使用采集策略: {}", traceId, strategy.getStrategyName());
            List<NovelBook> result = strategy.searchBooks(config, keyword);
            log.info("[{}] 搜索书籍成功，数量:{}，耗时:{}ms", traceId, result != null ? result.size() : 0, System.currentTimeMillis() - start);
            return result;
        } catch (Exception e) {
            log.error("[{}] 搜索书籍失败: {}", traceId, e.getMessage(), e);
            throw new ServiceException("搜索书籍失败: " + e.getMessage());
        }
    }

    /**
     * 获取书籍详情主流程
     *
     * @param config  爬虫规则配置
     * @param bookUrl 书籍详情页URL
     * @return 书籍详情对象
     * @throws ServiceException 采集异常
     */
    @Override
    public NovelBook getBookInfo(CrawlerRuleNovel config, String bookUrl) {
        validateConfig(config);
        validateString(bookUrl, "书籍详情页URL");
        long start = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString();
        log.info("[{}] 开始获取书籍详情: {}", traceId, bookUrl);
        try {
            CrawlerStrategy strategy = strategyFactory.getStrategy(config);
            log.debug("[{}] 使用采集策略: {}", traceId, strategy.getStrategyName());
            NovelBook book = strategy.getBookInfo(config, bookUrl);
            log.info("[{}] 获取书籍详情成功，耗时:{}ms", traceId, System.currentTimeMillis() - start);
            return book;
        } catch (Exception e) {
            log.error("[{}] 获取书籍详情失败: {}", traceId, e.getMessage(), e);
            throw new ServiceException("获取书籍详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取章节列表主流程
     *
     * @param config         爬虫规则配置
     * @param chapterListUrl 章节目录页URL
     * @return 章节列表
     * @throws ServiceException 采集异常
     */
    @Override
    public List<NovelBook.NovelChapter> getChapterList(CrawlerRuleNovel config, String chapterListUrl) {
        validateConfig(config);
        validateString(chapterListUrl, "章节目录页URL");
        long start = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString();
        log.info("[{}] 开始获取章节列表: {}", traceId, chapterListUrl);
        try {
            CrawlerStrategy strategy = strategyFactory.getStrategy(config);
            log.debug("[{}] 使用采集策略: {}", traceId, strategy.getStrategyName());
            List<NovelBook.NovelChapter> chapters = strategy.getChapterList(config, chapterListUrl);
            log.info("[{}] 获取章节列表成功，数量:{}，耗时:{}ms", traceId, chapters != null ? chapters.size() : 0, System.currentTimeMillis() - start);
            return chapters;
        } catch (Exception e) {
            log.error("[{}] 获取章节列表失败: {}", traceId, e.getMessage(), e);
            throw new ServiceException("获取章节列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个章节内容主流程（带重试）
     *
     * @param config     爬虫规则配置
     * @param chapterUrl 章节内容页URL
     * @return 章节内容对象
     * @throws ServiceException 采集异常
     */
    @Override
    public NovelBook.NovelChapter getChapterContent(CrawlerRuleNovel config, String chapterUrl) {
        validateConfig(config);
        validateString(chapterUrl, "章节内容页URL");
        long start = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString();
        log.debug("[{}] 开始获取章节内容: {}", traceId, chapterUrl);
        try {
            CrawlerStrategy strategy = strategyFactory.getStrategy(config);
            log.debug("[{}] 使用采集策略: {}", traceId, strategy.getStrategyName());
            // 带重试机制，网络异常自动重试3次
            NovelBook.NovelChapter chapter = networkManager.retry(() -> {
                try {
                    return strategy.getChapterContent(config, chapterUrl);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, 3);
            log.info("[{}] 获取章节内容成功，耗时:{}ms", traceId, System.currentTimeMillis() - start);
            return chapter;
        } catch (Exception e) {
            log.error("[{}] 章节内容采集失败: {}", traceId, e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "章节内容采集失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取章节内容主流程
     *
     * @param config      爬虫规则配置
     * @param chapterUrls 章节内容页URL列表
     * @return 章节内容对象列表
     * @throws ServiceException 采集异常
     */
    @Override
    public List<NovelBook.NovelChapter> getBatchChapterContent(CrawlerRuleNovel config, List<String> chapterUrls) {
        validateConfig(config);
        if (chapterUrls == null || chapterUrls.isEmpty()) {
            log.warn("章节URL列表为空");
            return Collections.emptyList();
        }
        long start = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString();
        log.info("[{}] 开始批量获取章节内容，章节数: {}", traceId, chapterUrls.size());
        // 使用全局线程池复用
        ExecutorService executor = com.shanhai.common.crawler.utils.CrawlerCoreManager.getCrawlerExecutor();
        try {
            List<CompletableFuture<NovelBook.NovelChapter>> futures = chapterUrls.stream()
                    .map(url -> CompletableFuture.supplyAsync(() -> processChapterContent(config, url, traceId), executor))
                    .collect(Collectors.toList());
            List<NovelBook.NovelChapter> result = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
            log.info("[{}] 批量获取章节内容完成，耗时:{}ms", traceId, System.currentTimeMillis() - start);
            return result;
        } finally {
            // 不再shutdown全局线程池
        }
    }

    /**
     * 处理单个章节内容（用于并发处理）
     *
     * @param config  爬虫规则配置
     * @param url     章节内容页URL
     * @param traceId 日志追踪ID
     * @return 章节内容对象
     */
    private NovelBook.NovelChapter processChapterContent(CrawlerRuleNovel config, String url, String traceId) {
        try {
            return getChapterContent(config, url);
        } catch (Exception e) {
            log.error("[{}] 获取章节内容失败: {}, 错误: {}", traceId, url, e.getMessage());
            NovelBook.NovelChapter errorChapter = new NovelBook.NovelChapter();
            errorChapter.setTitle("获取失败");
            errorChapter.setUrl(url);
            errorChapter.setContent("获取章节内容失败: " + e.getMessage());
            return errorChapter;
        }
    }
} 