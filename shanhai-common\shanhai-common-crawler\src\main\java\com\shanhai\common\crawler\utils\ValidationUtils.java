package com.shanhai.common.crawler.utils;

import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import lombok.extern.slf4j.Slf4j;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * 验证工具类
 * <p>
 * 提供各种参数验证和格式检查的工具方法。
 * 包含常用的验证逻辑，支持链式调用和批量验证。
 * 
 * <p>主要功能：
 * <ul>
 *   <li>基本参数验证：非空、长度、范围等</li>
 *   <li>格式验证：URL、邮箱、正则表达式等</li>
 *   <li>业务验证：标识符、选择器等</li>
 *   <li>集合验证：列表、映射等</li>
 * </ul>
 * 
 * <p>设计特点：
 * <ul>
 *   <li>支持链式调用，提高代码可读性</li>
 *   <li>提供详细的错误信息</li>
 *   <li>支持自定义验证规则</li>
 *   <li>线程安全的静态方法</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Slf4j
public class ValidationUtils {

    // ==================== 常用正则表达式 ====================

    /**
     * URL格式正则表达式
     */
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$"
    );

    /**
     * 标识符格式正则表达式（字母、数字、下划线、连字符）
     */
    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_-]{2,50}$"
    );

    /**
     * 邮箱格式正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    /**
     * CSS选择器基本格式正则表达式
     */
    private static final Pattern CSS_SELECTOR_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._#\\[\\]:()\\s,-]+$"
    );

    // ==================== 基本参数验证 ====================

    /**
     * 验证参数不为空
     * 
     * @param value 参数值
     * @param paramName 参数名称
     * @throws CrawlerException 参数为空时抛出异常
     */
    public static void requireNonNull(Object value, String paramName) {
        if (value == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不能为空");
        }
    }

    /**
     * 验证字符串不为空且不为空白
     * 
     * @param value 字符串值
     * @param paramName 参数名称
     * @throws CrawlerException 字符串为空或空白时抛出异常
     */
    public static void requireNonBlank(String value, String paramName) {
        if (value == null || value.trim().isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不能为空");
        }
    }

    /**
     * 验证集合不为空
     * 
     * @param collection 集合
     * @param paramName 参数名称
     * @throws CrawlerException 集合为空时抛出异常
     */
    public static void requireNonEmpty(Collection<?> collection, String paramName) {
        if (collection == null || collection.isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不能为空");
        }
    }

    /**
     * 验证映射不为空
     * 
     * @param map 映射
     * @param paramName 参数名称
     * @throws CrawlerException 映射为空时抛出异常
     */
    public static void requireNonEmpty(Map<?, ?> map, String paramName) {
        if (map == null || map.isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不能为空");
        }
    }

    // ==================== 长度和范围验证 ====================

    /**
     * 验证字符串长度
     * 
     * @param value 字符串值
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param paramName 参数名称
     * @throws CrawlerException 长度不符合要求时抛出异常
     */
    public static void requireLength(String value, int minLength, int maxLength, String paramName) {
        if (value == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不能为空");
        }
        
        int length = value.length();
        if (length < minLength || length > maxLength) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                String.format("%s长度必须在%d到%d字符之间，当前长度: %d", paramName, minLength, maxLength, length));
        }
    }

    /**
     * 验证数值范围
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @param paramName 参数名称
     * @throws CrawlerException 数值不在范围内时抛出异常
     */
    public static void requireRange(int value, int min, int max, String paramName) {
        if (value < min || value > max) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                String.format("%s必须在%d到%d之间，当前值: %d", paramName, min, max, value));
        }
    }

    /**
     * 验证长整型数值范围
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @param paramName 参数名称
     * @throws CrawlerException 数值不在范围内时抛出异常
     */
    public static void requireRange(long value, long min, long max, String paramName) {
        if (value < min || value > max) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                String.format("%s必须在%d到%d之间，当前值: %d", paramName, min, max, value));
        }
    }

    /**
     * 验证双精度数值范围
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @param paramName 参数名称
     * @throws CrawlerException 数值不在范围内时抛出异常
     */
    public static void requireRange(double value, double min, double max, String paramName) {
        if (value < min || value > max) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                String.format("%s必须在%.2f到%.2f之间，当前值: %.2f", paramName, min, max, value));
        }
    }

    /**
     * 验证正数
     * 
     * @param value 数值
     * @param paramName 参数名称
     * @throws CrawlerException 数值不是正数时抛出异常
     */
    public static void requirePositive(int value, String paramName) {
        if (value <= 0) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "必须是正数，当前值: " + value);
        }
    }

    /**
     * 验证非负数
     * 
     * @param value 数值
     * @param paramName 参数名称
     * @throws CrawlerException 数值是负数时抛出异常
     */
    public static void requireNonNegative(int value, String paramName) {
        if (value < 0) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不能是负数，当前值: " + value);
        }
    }

    // ==================== 格式验证 ====================

    /**
     * 验证URL格式
     * 
     * @param url URL字符串
     * @param paramName 参数名称
     * @throws CrawlerException URL格式不正确时抛出异常
     */
    public static void requireValidUrl(String url, String paramName) {
        requireNonBlank(url, paramName);
        
        if (!isValidUrl(url)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "格式不正确: " + url);
        }
    }

    /**
     * 验证标识符格式
     * 
     * @param identifier 标识符
     * @param paramName 参数名称
     * @throws CrawlerException 标识符格式不正确时抛出异常
     */
    public static void requireValidIdentifier(String identifier, String paramName) {
        requireNonBlank(identifier, paramName);
        
        if (!isValidIdentifier(identifier)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "格式不正确，只能包含字母、数字、下划线、连字符，长度2-50字符: " + identifier);
        }
    }

    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @param paramName 参数名称
     * @throws CrawlerException 邮箱格式不正确时抛出异常
     */
    public static void requireValidEmail(String email, String paramName) {
        requireNonBlank(email, paramName);
        
        if (!isValidEmail(email)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "格式不正确: " + email);
        }
    }

    /**
     * 验证正则表达式格式
     * 
     * @param regex 正则表达式
     * @param paramName 参数名称
     * @throws CrawlerException 正则表达式格式不正确时抛出异常
     */
    public static void requireValidRegex(String regex, String paramName) {
        if (regex == null || regex.trim().isEmpty()) {
            return; // 允许为空
        }
        
        try {
            Pattern.compile(regex);
        } catch (PatternSyntaxException e) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "正则表达式格式不正确: " + e.getMessage());
        }
    }

    /**
     * 验证CSS选择器格式
     * 
     * @param selector CSS选择器
     * @param paramName 参数名称
     * @throws CrawlerException CSS选择器格式不正确时抛出异常
     */
    public static void requireValidCssSelector(String selector, String paramName) {
        requireNonBlank(selector, paramName);
        
        if (!isValidCssSelector(selector)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "CSS选择器格式不正确: " + selector);
        }
    }

    // ==================== 业务验证 ====================

    /**
     * 验证HTTP方法
     * 
     * @param method HTTP方法
     * @param paramName 参数名称
     * @throws CrawlerException HTTP方法不支持时抛出异常
     */
    public static void requireValidHttpMethod(String method, String paramName) {
        requireNonBlank(method, paramName);
        
        String upperMethod = method.toUpperCase();
        if (!isValidHttpMethod(upperMethod)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不支持的HTTP方法: " + method);
        }
    }

    /**
     * 验证字符编码
     * 
     * @param charset 字符编码
     * @param paramName 参数名称
     * @throws CrawlerException 字符编码不支持时抛出异常
     */
    public static void requireValidCharset(String charset, String paramName) {
        if (charset == null || charset.trim().isEmpty()) {
            return; // 允许为空，使用默认编码
        }
        
        try {
            java.nio.charset.Charset.forName(charset);
        } catch (Exception e) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                paramName + "不支持的字符编码: " + charset);
        }
    }

    /**
     * 验证列表大小
     * 
     * @param list 列表
     * @param maxSize 最大大小
     * @param paramName 参数名称
     * @throws CrawlerException 列表大小超过限制时抛出异常
     */
    public static void requireMaxSize(List<?> list, int maxSize, String paramName) {
        if (list != null && list.size() > maxSize) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                String.format("%s列表大小不能超过%d，当前大小: %d", paramName, maxSize, list.size()));
        }
    }

    // ==================== 格式检查方法 ====================

    /**
     * 检查URL格式是否有效
     * 
     * @param url URL字符串
     * @return 是否有效
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            new URL(url);
            return URL_PATTERN.matcher(url).matches();
        } catch (MalformedURLException e) {
            return false;
        }
    }

    /**
     * 检查标识符格式是否有效
     * 
     * @param identifier 标识符
     * @return 是否有效
     */
    public static boolean isValidIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }
        return IDENTIFIER_PATTERN.matcher(identifier).matches();
    }

    /**
     * 检查邮箱格式是否有效
     * 
     * @param email 邮箱地址
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 检查CSS选择器格式是否有效
     * 
     * @param selector CSS选择器
     * @return 是否有效
     */
    public static boolean isValidCssSelector(String selector) {
        if (selector == null || selector.trim().isEmpty()) {
            return false;
        }
        return CSS_SELECTOR_PATTERN.matcher(selector).matches();
    }

    /**
     * 检查HTTP方法是否有效
     * 
     * @param method HTTP方法
     * @return 是否有效
     */
    public static boolean isValidHttpMethod(String method) {
        if (method == null || method.trim().isEmpty()) {
            return false;
        }
        
        String upperMethod = method.toUpperCase();
        return "GET".equals(upperMethod) || "POST".equals(upperMethod) || 
               "PUT".equals(upperMethod) || "DELETE".equals(upperMethod) ||
               "HEAD".equals(upperMethod) || "OPTIONS".equals(upperMethod) ||
               "PATCH".equals(upperMethod);
    }

    /**
     * 检查正则表达式是否有效
     * 
     * @param regex 正则表达式
     * @return 是否有效
     */
    public static boolean isValidRegex(String regex) {
        if (regex == null || regex.trim().isEmpty()) {
            return true; // 空正则表达式认为是有效的
        }
        
        try {
            Pattern.compile(regex);
            return true;
        } catch (PatternSyntaxException e) {
            return false;
        }
    }

    // ==================== 链式验证器 ====================

    /**
     * 创建链式验证器
     * 
     * @param value 待验证的值
     * @param paramName 参数名称
     * @return 验证器实例
     */
    public static Validator<String> validate(String value, String paramName) {
        return new Validator<>(value, paramName);
    }

    /**
     * 创建整数链式验证器
     * 
     * @param value 待验证的值
     * @param paramName 参数名称
     * @return 验证器实例
     */
    public static Validator<Integer> validate(Integer value, String paramName) {
        return new Validator<>(value, paramName);
    }

    /**
     * 链式验证器类
     * 
     * @param <T> 值类型
     */
    public static class Validator<T> {
        private final T value;
        private final String paramName;

        public Validator(T value, String paramName) {
            this.value = value;
            this.paramName = paramName;
        }

        /**
         * 验证非空
         * 
         * @return 验证器实例
         */
        public Validator<T> nonNull() {
            requireNonNull(value, paramName);
            return this;
        }

        /**
         * 验证字符串非空白
         * 
         * @return 验证器实例
         */
        public Validator<T> nonBlank() {
            if (value instanceof String) {
                requireNonBlank((String) value, paramName);
            }
            return this;
        }

        /**
         * 验证字符串长度
         * 
         * @param minLength 最小长度
         * @param maxLength 最大长度
         * @return 验证器实例
         */
        public Validator<T> length(int minLength, int maxLength) {
            if (value instanceof String) {
                requireLength((String) value, minLength, maxLength, paramName);
            }
            return this;
        }

        /**
         * 验证URL格式
         * 
         * @return 验证器实例
         */
        public Validator<T> url() {
            if (value instanceof String) {
                requireValidUrl((String) value, paramName);
            }
            return this;
        }

        /**
         * 验证标识符格式
         * 
         * @return 验证器实例
         */
        public Validator<T> identifier() {
            if (value instanceof String) {
                requireValidIdentifier((String) value, paramName);
            }
            return this;
        }

        /**
         * 验证正数
         * 
         * @return 验证器实例
         */
        public Validator<T> positive() {
            if (value instanceof Integer) {
                requirePositive((Integer) value, paramName);
            }
            return this;
        }

        /**
         * 验证范围
         * 
         * @param min 最小值
         * @param max 最大值
         * @return 验证器实例
         */
        public Validator<T> range(int min, int max) {
            if (value instanceof Integer) {
                requireRange((Integer) value, min, max, paramName);
            }
            return this;
        }

        /**
         * 获取验证后的值
         * 
         * @return 值
         */
        public T get() {
            return value;
        }
    }
}
