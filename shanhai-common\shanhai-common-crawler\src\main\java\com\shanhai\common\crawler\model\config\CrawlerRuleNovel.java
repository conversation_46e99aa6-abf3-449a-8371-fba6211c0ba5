package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shanhai.common.core.model.BaseEntity;
import com.tangzc.autotable.annotation.mysql.MysqlTypeConstant;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 小说爬虫规则主类
 * <p>
 * 作为所有规则配置的入口，聚合各子配置。支持配置校验和业务规则检查。
 * 线程安全：仅为数据结构，线程安全。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule", comment = "小说爬虫规则主表")
@TableName("crawler_rule")
public class CrawlerRuleNovel extends BaseEntity<CrawlerRuleNovel> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点名称
     */
    @NotBlank(message = "站点名称不能为空")
    @Column(value = "source_name", type = MysqlTypeConstant.VARCHAR, comment = "站点名称", length = 50)
    private String sourceName;

    /**
     * 站点主页URL
     */
    @NotBlank(message = "站点URL不能为空")
    @Column(value = "source_url", type = MysqlTypeConstant.VARCHAR, comment = "站点主页URL", length = 500)
    private String sourceUrl;

    /**
     * User-Agent配置（可选）
     */
    @Column(value = "user_agent", type = MysqlTypeConstant.VARCHAR, comment = "User-Agent配置", length = 500)
    private String userAgent;

    /**
     * 请求头配置（可选）
     */
    @Column(value = "headers", type = MysqlTypeConstant.VARCHAR, comment = "请求头配置(JSON格式)", length = 500)
    private Map<String, String> headers;

    /**
     * Cookie配置（可选）
     */
    @Column(value = "cookies", type = MysqlTypeConstant.VARCHAR, comment = "Cookie配置(JSON格式)", length = 500)
    private Map<String, String> cookies;

    /**
     * 超时时间（毫秒，可选）
     */
    @Column(value = "timeout", type = MysqlTypeConstant.BIGINT, comment = "超时时间(毫秒)", length = 10)
    private Long timeout;

    /**
     * 采集模式（API/HTML/SELENIUM/AUTO）
     */
    @NotBlank(message = "采集模式不能为空")
    @Column(value = "mode", comment = "采集模式", length = 10)
    private String mode;

    /**
     * 搜索配置ID
     */
    @Column(value = "rule_search_id", type = MysqlTypeConstant.VARCHAR, comment = "搜索配置ID", length = 32)
    private String ruleSearchId;
    private transient CrawlerRuleSearch crawlerRuleSearch;

    /**
     * 书籍详情配置ID
     */
    @Column(value = "rule_book_info_id", type = MysqlTypeConstant.VARCHAR, comment = "书籍详情配置ID", length = 32)
    private String ruleBookInfoId;
    private transient CrawlerRuleBookInfo crawlerRuleBookInfo;


    /**
     * 章节列表配置ID
     */
    @Column(value = "rule_chapter_id", type = MysqlTypeConstant.VARCHAR, comment = "章节列表配置ID", length = 32)
    private String ruleChapterId;
    private transient CrawlerRuleChapter crawlerRuleChapter;


    /**
     * 正文段落配置ID
     */
    @Column(value = "rule_content_id", type = MysqlTypeConstant.VARCHAR, comment = "正文段落配置ID", length = 32)
    private String ruleContentId;
    private transient CrawlerRuleContent crawlerRuleContent;

    /**
     * 反爬虫配置ID
     */
    @Column(value = "rule_anti_spider_id", type = MysqlTypeConstant.VARCHAR, comment = "反爬虫配置ID", length = 32)
    private String ruleAntiSpiderId;
    private transient CrawlerRuleAntiSpider crawlerRuleAntiSpider;
} 