package com.shanhai.common.crawler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.config.*;
import com.shanhai.common.crawler.repository.NovelCrawlerRuleMapper;
import com.shanhai.common.crawler.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class NovelCrawlerRuleServiceImpl
        extends ServiceImpl<NovelCrawlerRuleMapper, CrawlerRuleNovel>
        implements NovelCrawlerRuleService {
    private final RuleSearchService ruleSearchService;
    private final RuleBookInfoService ruleBookInfoService;
    private final ChapterListService chapterListService;
    private final RuleContentService ruleContentService;
    private final AntiSpiderService antiSpiderService;

    public NovelCrawlerRuleServiceImpl(
            RuleSearchService ruleSearchService,
            RuleBookInfoService ruleBookInfoService,
            ChapterListService chapterListService,
            RuleContentService ruleContentService,
            AntiSpiderService antiSpiderService) {
        this.ruleSearchService = ruleSearchService;
        this.ruleBookInfoService = ruleBookInfoService;
        this.chapterListService = chapterListService;
        this.ruleContentService = ruleContentService;
        this.antiSpiderService = antiSpiderService;
    }

    @Override
    @Transactional
    public boolean saveRule(CrawlerRuleNovel rule) {
        this.save(rule); // 使用 MyBatis-Plus 的 save
        String ruleId = rule.getId();
        if (rule.getCrawlerRuleSearch() != null) {
            rule.getCrawlerRuleSearch().setRuleId(ruleId);
            ruleSearchService.save(rule.getCrawlerRuleSearch());
        }
        if (rule.getCrawlerRuleBookInfo() != null) {
            rule.getCrawlerRuleBookInfo().setRuleId(ruleId);
            ruleBookInfoService.save(rule.getCrawlerRuleBookInfo());
        }
        if (rule.getCrawlerRuleChapter() != null) {
            rule.getCrawlerRuleChapter().setRuleId(ruleId);
            chapterListService.save(rule.getCrawlerRuleChapter());
        }
        if (rule.getCrawlerRuleContent() != null) {
            rule.getCrawlerRuleContent().setRuleId(ruleId);
            ruleContentService.save(rule.getCrawlerRuleContent());
        }
        if (rule.getCrawlerRuleAntiSpider() != null) {
            rule.getCrawlerRuleAntiSpider().setRuleId(ruleId);
            antiSpiderService.save(rule.getCrawlerRuleAntiSpider());
        }
        return true;
    }

    @Override
    public CrawlerRuleNovel loadRuleById(String id) {
        CrawlerRuleNovel rule = this.getById(id);
        if (rule == null) return null;
        rule.setCrawlerRuleSearch(ruleSearchService.getOne(new LambdaQueryWrapper<CrawlerRuleSearch>().eq(CrawlerRuleSearch::getRuleId, id)));
        rule.setCrawlerRuleBookInfo(ruleBookInfoService.getOne(new LambdaQueryWrapper<CrawlerRuleBookInfo>().eq(CrawlerRuleBookInfo::getRuleId, id)));
        rule.setCrawlerRuleChapter(chapterListService.getOne(new LambdaQueryWrapper<CrawlerRuleChapter>().eq(CrawlerRuleChapter::getRuleId, id)));
        rule.setCrawlerRuleContent(ruleContentService.getOne(new LambdaQueryWrapper<CrawlerRuleContent>().eq(CrawlerRuleContent::getRuleId, id)));
        rule.setCrawlerRuleAntiSpider(antiSpiderService.getOne(new LambdaQueryWrapper<CrawlerRuleAntiSpider>().eq(CrawlerRuleAntiSpider::getRuleId, id)));
        return rule;
    }

    @Override
    public List<CrawlerRuleNovel> loadAllRules() {
        List<CrawlerRuleNovel> rules = this.list();
        for (CrawlerRuleNovel rule : rules) {
            String id = rule.getId();
            rule.setCrawlerRuleSearch(ruleSearchService.getOne(new LambdaQueryWrapper<CrawlerRuleSearch>().eq(CrawlerRuleSearch::getRuleId, id)));
            rule.setCrawlerRuleBookInfo(ruleBookInfoService.getOne(new LambdaQueryWrapper<CrawlerRuleBookInfo>().eq(CrawlerRuleBookInfo::getRuleId, id)));
            rule.setCrawlerRuleChapter(chapterListService.getOne(new LambdaQueryWrapper<CrawlerRuleChapter>().eq(CrawlerRuleChapter::getRuleId, id)));
            rule.setCrawlerRuleContent(ruleContentService.getOne(new LambdaQueryWrapper<CrawlerRuleContent>().eq(CrawlerRuleContent::getRuleId, id)));
            rule.setCrawlerRuleAntiSpider(antiSpiderService.getOne(new LambdaQueryWrapper<CrawlerRuleAntiSpider>().eq(CrawlerRuleAntiSpider::getRuleId, id)));
        }
        return rules;
    }

    @Override
    @Transactional
    public boolean updateRule(CrawlerRuleNovel rule) {
        this.updateById(rule);
        String ruleId = rule.getId();
        if (rule.getCrawlerRuleSearch() != null) {
            ruleSearchService.update(rule.getCrawlerRuleSearch(), new LambdaQueryWrapper<CrawlerRuleSearch>().eq(CrawlerRuleSearch::getRuleId, ruleId));
        }
        if (rule.getCrawlerRuleBookInfo() != null) {
            ruleBookInfoService.update(rule.getCrawlerRuleBookInfo(), new LambdaQueryWrapper<CrawlerRuleBookInfo>().eq(CrawlerRuleBookInfo::getRuleId, ruleId));
        }
        if (rule.getCrawlerRuleChapter() != null) {
            chapterListService.update(rule.getCrawlerRuleChapter(), new LambdaQueryWrapper<CrawlerRuleChapter>().eq(CrawlerRuleChapter::getRuleId, ruleId));
        }
        if (rule.getCrawlerRuleContent() != null) {
            ruleContentService.update(rule.getCrawlerRuleContent(), new LambdaQueryWrapper<CrawlerRuleContent>().eq(CrawlerRuleContent::getRuleId, ruleId));
        }
        if (rule.getCrawlerRuleAntiSpider() != null) {
            antiSpiderService.update(rule.getCrawlerRuleAntiSpider(), new LambdaQueryWrapper<CrawlerRuleAntiSpider>().eq(CrawlerRuleAntiSpider::getRuleId, ruleId));
        }
        return true;
    }

    @Override
    @Transactional
    public boolean deleteRuleById(String id) {
        ruleSearchService.remove(new LambdaQueryWrapper<CrawlerRuleSearch>().eq(CrawlerRuleSearch::getRuleId, id));
        ruleBookInfoService.remove(new LambdaQueryWrapper<CrawlerRuleBookInfo>().eq(CrawlerRuleBookInfo::getRuleId, id));
        chapterListService.remove(new LambdaQueryWrapper<CrawlerRuleChapter>().eq(CrawlerRuleChapter::getRuleId, id));
        ruleContentService.remove(new LambdaQueryWrapper<CrawlerRuleContent>().eq(CrawlerRuleContent::getRuleId, id));
        antiSpiderService.remove(new LambdaQueryWrapper<CrawlerRuleAntiSpider>().eq(CrawlerRuleAntiSpider::getRuleId, id));
        this.removeById(id);
        return true;
    }

    @Override
    public List<CrawlerRuleNovel> exportRules() {
        return loadAllRules();
    }

    @Override
    @Transactional
    public boolean importRules(List<CrawlerRuleNovel> rules) {
        for (CrawlerRuleNovel rule : rules) {
            this.saveRule(rule);
        }
        return true;
    }
} 