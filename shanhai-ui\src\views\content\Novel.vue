<template>
  <div class="novel-page">
    <el-card class="fade-in">
      <div slot="header" class="header-bar">
        <span><i class="el-icon-reading"></i> 小说管理</span>
        <el-button type="primary" icon="el-icon-plus" class="animated-btn" @click="handleAdd">
          新增小说
        </el-button>
      </div>
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item>
          <span class="form-label"><i class="el-icon-user"></i> 作者</span>
          <el-input v-model="searchForm.author" placeholder="请输入作者" clearable />
        </el-form-item>
        <el-form-item>
          <span class="form-label"><i class="el-icon-notebook-2"></i> 小说名</span>
          <el-input v-model="searchForm.title" placeholder="请输入小说名" clearable />
        </el-form-item>
        <el-form-item>
          <span class="form-label"><i class="el-icon-collection-tag"></i> 状态</span>
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="连载中" value="连载中" />
            <el-option label="已完结" value="已完结" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" class="animated-btn" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" class="animated-btn" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 表格 -->
      <el-table :data="novelList" style="width: 100%" class="novel-table" stripe highlight-current-row>
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="title" label="小说名称" min-width="120" />
        <el-table-column prop="author" label="作者" min-width="100" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '连载中' ? 'success' : 'info'" effect="dark">
              <i :class="scope.row.status === '连载中' ? 'el-icon-loading' : 'el-icon-finished'" style="margin-right:4px;"></i>
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="140" />
        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" class="animated-btn" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="animated-btn" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Novel',
  data() {
    return {
      searchForm: {
        author: '',
        title: '',
        status: ''
      },
      novelList: [
        {
          id: 1,
          title: '修真聊天群',
          author: '圣骑士的传说',
          status: '连载中',
          updateTime: '2024-01-15 10:30'
        },
        {
          id: 2,
          title: '斗破苍穹',
          author: '天蚕土豆',
          status: '已完结',
          updateTime: '2024-01-14 15:20'
        },
        {
          id: 3,
          title: '遮天',
          author: '辰东',
          status: '已完结',
          updateTime: '2024-01-13 09:45'
        }
      ]
    }
  },
  methods: {
    handleAdd() {
      this.$message({ message: '新增小说功能待实现', type: 'info', showClose: true })
    },
    handleEdit(row) {
      this.$message({ message: `编辑小说：${row.title}`, type: 'info', showClose: true })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除小说"${row.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSearch() {
      this.$message.info('搜索功能待实现')
    },
    handleReset() {
      this.searchForm = {
        author: '',
        title: '',
        status: ''
      }
      this.$message.info('重置成功')
    }
  }
}
</script>

<style scoped>
.novel-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
  animation: fadeIn 0.6s;
}
.header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 600;
}
.animated-btn {
  transition: all 0.2s;
  border-radius: 20px;
}
.animated-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 2px 8px #b3c6ff44;
}
.fade-in {
  animation: fadeInUp 0.5s;
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.novel-table ::v-deep .el-table__row {
  transition: background 0.2s;
}
.novel-table ::v-deep .el-table__row:hover {
  background: #e6f7ff !important;
}
.search-form {
  margin-bottom: 18px;
  padding: 18px 18px 0 18px;
  background: #fafdff;
  border-radius: 8px;
  box-shadow: 0 1px 4px #e3e8f7;
  animation: fadeInUp 0.5s;
  display: flex;
  flex-wrap: wrap;
  gap: 12px 24px;
}
.form-label {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  margin-right: 8px;
  color: #606266;
}
.form-label i {
  margin-right: 4px;
  font-size: 16px;
}
</style> 