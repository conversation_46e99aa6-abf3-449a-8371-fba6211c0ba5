<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ 'sidebar-collapsed': isCollapsed }">
      <div class="logo">
        <span v-if="!isCollapsed">山海管理系统</span>
        <span v-else>山海</span>
      </div>
      <el-menu
        :default-active="$route.path"
        :collapse="isCollapsed"
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
        unique-opened
      >
        <el-menu-item index="/dashboard">
          <i class="el-icon-s-home"></i>
          <span slot="title">首页</span>
        </el-menu-item>
        <el-submenu index="system">
          <template slot="title">
            <i class="el-icon-s-tools"></i>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/system/user">
            <i class="el-icon-user"></i>
            <span slot="title">用户管理</span>
          </el-menu-item>
          <el-menu-item index="/system/role">
            <i class="el-icon-s-check"></i>
            <span slot="title">角色管理</span>
          </el-menu-item>
          <el-menu-item index="/system/menu">
            <i class="el-icon-menu"></i>
            <span slot="title">菜单管理</span>
          </el-menu-item>
        </el-submenu>
        <el-submenu index="content">
          <template slot="title">
            <i class="el-icon-document"></i>
            <span>内容管理</span>
          </template>
          <el-menu-item index="/content/novel">
            <i class="el-icon-reading"></i>
            <span slot="title">小说管理</span>
          </el-menu-item>
          <el-menu-item index="/content/rule">
            <i class="el-icon-setting"></i>
            <span slot="title">规则管理</span>
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </div>
    <!-- 主内容区 -->
    <div class="main-container">
      <div class="header">
        <div class="header-left">
          <el-button type="text" class="collapse-btn" @click="toggleCollapse">
            <i :class="isCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
          </el-button>
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.meta && $route.meta.title">{{ $route.meta.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="user-info">
              <el-avatar size="small">
                <i class="el-icon-user"></i>
              </el-avatar>
              <span class="username">管理员</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i>个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <i class="el-icon-setting"></i>系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="el-icon-switch-button"></i>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <global-tabs
        :tabs="tabs"
        :active-tab="activeTab"
        @tab-change="handleTabChange"
        @tab-remove="handleTabRemove"
        @tab-refresh="handleTabRefresh"
        @tab-close-others="handleTabCloseOthers"
        @tab-close-all="handleTabCloseAll"
        @tab-fix="handleTabFix"
        @tab-unfix="handleTabUnfix"
      />
      <div class="content-container">
        <transition name="fade-slide" mode="out-in">
          <router-view :key="$route.fullPath" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import GlobalTabs from './GlobalTabs.vue'
export default {
  name: 'Layout',
  components: { GlobalTabs },
  data() {
    return {
      isCollapsed: false,
      tabs: [
        {
          name: 'dashboard',
          fullPath: '/dashboard',
          meta: { title: '首页' },
          fixed: false,
          icon: 'el-icon-s-home'
        }
      ],
      activeTab: '/dashboard'
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(route) {
        this.addTab(route)
        console.log('watch $route, tabs:', this.tabs)
      }
    }
  },
  mounted() {
    this.handleResize()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },
    handleResize() {
      if (window.innerWidth < 900) {
        this.isCollapsed = true
      }
    },
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人中心功能开发中...')
          break
        case 'settings':
          this.$message.info('系统设置功能开发中...')
          break
        case 'logout':
          this.$confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$store.dispatch('logout')
            this.$router.push('/login')
            this.$message.success('退出成功')
          }).catch(() => {})
          break
      }
    },
    addTab(route) {
      const exists = this.tabs.find(tab => tab.fullPath === route.fullPath)
      if (!exists) {
        this.tabs.push({
          name: route.name,
          fullPath: route.fullPath,
          meta: route.meta,
          icon: route.meta && route.meta.icon,
          fixed: false
        })
      }
      this.activeTab = route.fullPath
      console.log('addTab, tabs:', this.tabs)
    },
    handleTabChange(fullPath) {
      if (fullPath !== this.$route.fullPath) {
        this.$router.push(fullPath)
      }
    },
    handleTabRemove(fullPath) {
      const idx = this.tabs.findIndex(tab => tab.fullPath === fullPath)
      if (idx > -1) {
        this.tabs.splice(idx, 1)
        let nextTab = this.tabs[idx - 1] || this.tabs[0]
        if (nextTab && nextTab.fullPath !== this.$route.fullPath) {
          this.$router.push(nextTab.fullPath)
        }
      }
      console.log('handleTabRemove, tabs:', this.tabs)
    },
    handleTabRefresh(fullPath) {
      if (fullPath === this.$route.fullPath) {
        this.$router.replace({ path: '/redirect' + fullPath }).then(() => {
          this.$router.replace(fullPath)
        })
      } else {
        this.$router.push(fullPath)
      }
    },
    handleTabCloseOthers(fullPath) {
      this.tabs = this.tabs.filter(tab => tab.fullPath === fullPath || tab.fixed)
      if (fullPath !== this.$route.fullPath) {
        this.$router.push(fullPath)
      }
      console.log('handleTabCloseOthers, tabs:', this.tabs)
    },
    handleTabCloseAll() {
      this.tabs = this.tabs.filter(tab => tab.fixed)
      if (this.tabs.length > 0 && this.tabs[0].fullPath !== this.$route.fullPath) {
        this.$router.push(this.tabs[0].fullPath)
      }
      console.log('handleTabCloseAll, tabs:', this.tabs)
    },
    handleTabFix(fullPath) {
      const tab = this.tabs.find(tab => tab.fullPath === fullPath)
      if (tab) tab.fixed = true
      console.log('handleTabFix, tabs:', this.tabs)
    },
    handleTabUnfix(fullPath) {
      const tab = this.tabs.find(tab => tab.fullPath === fullPath)
      if (tab) tab.fixed = false
      console.log('handleTabUnfix, tabs:', this.tabs)
    }
  }
}
</script>

<style scoped>
.layout-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}
.sidebar {
  width: 220px;
  height: 100vh;
  background: linear-gradient(180deg, #304156 0%, #263445 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1000;
  overflow: hidden;
}
.sidebar-collapsed {
  width: 64px;
}
.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 16px;
  overflow: hidden;
}
.logo span {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s;
}
.sidebar-menu {
  border: none;
  background: transparent;
  height: calc(100vh - 60px);
  overflow-y: auto;
}
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}
.sidebar-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}
.sidebar-menu .el-menu-item,
.sidebar-menu .el-submenu__title {
  height: 48px;
  line-height: 48px;
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  color: #bfcbd9;
}
.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-submenu__title:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
  transform: translateX(4px);
}
.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(90deg, #409EFF 0%, #36a3f7 100%) !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}
.sidebar-menu .el-menu-item i,
.sidebar-menu .el-submenu__title i {
  margin-right: 12px;
  font-size: 16px;
  width: 16px;
  text-align: center;
}
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  overflow: hidden;
}
.header {
  height: 56px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #e8eaec;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 999;
}
.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}
.collapse-btn {
  margin-right: 16px;
}
.breadcrumb {
  font-size: 14px;
}
.breadcrumb .el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: #409EFF;
  font-weight: 600;
}
.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 200px;
}
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}
.user-info:hover {
  background: #f5f7fa;
}
.username {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}
.content-container {
  flex: 1;
  padding: 24px;
  background: #f5f7fa;
  overflow: auto;
}
.content-container::-webkit-scrollbar {
  width: 8px;
}
.content-container::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 4px;
}
.content-container::-webkit-scrollbar-thumb:hover {
  background: #c0c4cc;
}
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-slide-enter,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
@media (max-width: 900px) {
  .sidebar {
    width: 64px !important;
  }
  .main-container {
    padding: 0;
  }
  .content-container {
    padding: 8px;
  }
}
</style> 