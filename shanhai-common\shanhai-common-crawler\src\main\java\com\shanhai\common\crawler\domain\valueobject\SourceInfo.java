package com.shanhai.common.crawler.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Objects;

/**
 * 数据源信息值对象
 * <p>
 * 封装爬虫数据源的基本信息，包括标识符、名称、基础URL等。
 * 作为值对象，具有不可变性和值相等性的特征。
 * 
 * <p>主要职责：
 * <ul>
 *   <li>封装数据源的基本标识信息</li>
 *   <li>提供数据源信息的验证逻辑</li>
 *   <li>确保数据源信息的一致性和完整性</li>
 * </ul>
 * 
 * <p>设计原则：
 * <ul>
 *   <li>值对象具有不可变性</li>
 *   <li>基于值进行相等性比较</li>
 *   <li>包含业务验证逻辑</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源标识符
     * 用于唯一标识一个数据源，通常使用域名或简短标识
     * 格式要求：字母、数字、下划线、连字符，长度2-50字符
     */
    @NotBlank(message = "数据源标识符不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{2,50}$", message = "数据源标识符格式不正确，只能包含字母、数字、下划线、连字符，长度2-50字符")
    private String identifier;

    /**
     * 数据源名称
     * 用于显示的友好名称
     */
    @NotBlank(message = "数据源名称不能为空")
    private String name;

    /**
     * 数据源基础URL
     * 数据源的主页或API基础地址
     */
    @NotBlank(message = "数据源基础URL不能为空")
    @Pattern(regexp = "^https?://.*", message = "数据源基础URL必须以http://或https://开头")
    private String baseUrl;

    /**
     * 数据源描述
     * 对数据源的详细说明
     */
    private String description;

    /**
     * 数据源类型
     * 如：小说站点、漫画站点、新闻站点等
     */
    private String sourceType;

    /**
     * 数据源语言
     * 如：zh-CN、en-US等
     */
    @Builder.Default
    private String language = "zh-CN";

    /**
     * 数据源编码
     * 如：UTF-8、GBK等
     */
    @Builder.Default
    private String charset = "UTF-8";

    // ==================== 业务方法 ====================

    /**
     * 验证数据源信息的完整性
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        return identifier != null && !identifier.trim().isEmpty()
            && name != null && !name.trim().isEmpty()
            && baseUrl != null && !baseUrl.trim().isEmpty()
            && isValidUrl(baseUrl)
            && isValidIdentifier(identifier);
    }

    /**
     * 验证标识符格式
     * 
     * @param identifier 标识符
     * @return 是否有效
     */
    private boolean isValidIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }
        return identifier.matches("^[a-zA-Z0-9_-]{2,50}$");
    }

    /**
     * 验证URL格式
     * 
     * @param url URL字符串
     * @return 是否有效
     */
    private boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        return url.matches("^https?://.*");
    }

    /**
     * 获取域名
     * 从基础URL中提取域名部分
     * 
     * @return 域名，如果提取失败返回null
     */
    public String getDomain() {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            return null;
        }
        
        try {
            String url = baseUrl.trim();
            // 移除协议部分
            if (url.startsWith("https://")) {
                url = url.substring(8);
            } else if (url.startsWith("http://")) {
                url = url.substring(7);
            }
            
            // 提取域名部分（到第一个/或?或#为止）
            int slashIndex = url.indexOf('/');
            int questionIndex = url.indexOf('?');
            int hashIndex = url.indexOf('#');
            
            int endIndex = url.length();
            if (slashIndex > 0) endIndex = Math.min(endIndex, slashIndex);
            if (questionIndex > 0) endIndex = Math.min(endIndex, questionIndex);
            if (hashIndex > 0) endIndex = Math.min(endIndex, hashIndex);
            
            return url.substring(0, endIndex);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取协议
     * 从基础URL中提取协议部分
     * 
     * @return 协议（http或https），如果提取失败返回http
     */
    public String getProtocol() {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            return "http";
        }
        
        String url = baseUrl.trim().toLowerCase();
        if (url.startsWith("https://")) {
            return "https";
        } else if (url.startsWith("http://")) {
            return "http";
        } else {
            return "http"; // 默认协议
        }
    }

    /**
     * 检查是否为HTTPS协议
     * 
     * @return 是否为HTTPS
     */
    public boolean isHttps() {
        return "https".equals(getProtocol());
    }

    /**
     * 构建完整URL
     * 将相对路径与基础URL组合成完整URL
     * 
     * @param relativePath 相对路径
     * @return 完整URL
     */
    public String buildFullUrl(String relativePath) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            return relativePath;
        }
        
        if (relativePath == null || relativePath.trim().isEmpty()) {
            return baseUrl;
        }
        
        String base = baseUrl.trim();
        String path = relativePath.trim();
        
        // 如果相对路径已经是完整URL，直接返回
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return path;
        }
        
        // 确保基础URL不以/结尾，相对路径以/开头
        if (base.endsWith("/")) {
            base = base.substring(0, base.length() - 1);
        }
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        
        return base + path;
    }

    /**
     * 获取显示名称
     * 如果名称为空，返回标识符
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (name != null && !name.trim().isEmpty()) {
            return name.trim();
        }
        return identifier != null ? identifier : "未知数据源";
    }

    /**
     * 获取有效的字符编码
     * 如果编码为空或无效，返回UTF-8
     * 
     * @return 字符编码
     */
    public String getEffectiveCharset() {
        if (charset == null || charset.trim().isEmpty()) {
            return "UTF-8";
        }
        
        String cs = charset.trim().toUpperCase();
        // 常见编码的标准化
        switch (cs) {
            case "UTF8":
                return "UTF-8";
            case "GBK":
            case "GB2312":
                return "GBK";
            case "ISO-8859-1":
            case "LATIN1":
                return "ISO-8859-1";
            default:
                return charset.trim();
        }
    }

    // ==================== Object方法重写 ====================

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SourceInfo that = (SourceInfo) o;
        return Objects.equals(identifier, that.identifier) &&
               Objects.equals(name, that.name) &&
               Objects.equals(baseUrl, that.baseUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(identifier, name, baseUrl);
    }

    @Override
    public String toString() {
        return "SourceInfo{" +
                "identifier='" + identifier + '\'' +
                ", name='" + name + '\'' +
                ", baseUrl='" + baseUrl + '\'' +
                ", sourceType='" + sourceType + '\'' +
                ", language='" + language + '\'' +
                '}';
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建数据源信息
     * 
     * @param identifier 标识符
     * @param name 名称
     * @param baseUrl 基础URL
     * @return 数据源信息对象
     */
    public static SourceInfo of(String identifier, String name, String baseUrl) {
        return SourceInfo.builder()
                .identifier(identifier)
                .name(name)
                .baseUrl(baseUrl)
                .build();
    }

    /**
     * 创建数据源信息（包含描述）
     * 
     * @param identifier 标识符
     * @param name 名称
     * @param baseUrl 基础URL
     * @param description 描述
     * @return 数据源信息对象
     */
    public static SourceInfo of(String identifier, String name, String baseUrl, String description) {
        return SourceInfo.builder()
                .identifier(identifier)
                .name(name)
                .baseUrl(baseUrl)
                .description(description)
                .build();
    }
}
