package com.shanhai.common.crawler.network;

import lombok.Builder;
import lombok.Data;

/**
 * 网络请求统计信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class NetworkStats {
    
    /**
     * 总请求数
     */
    private long totalRequests;
    
    /**
     * 成功请求数
     */
    private long successfulRequests;
    
    /**
     * 失败请求数
     */
    private long failedRequests;
    
    /**
     * 平均响应时间（毫秒）
     */
    private double averageResponseTime;
    
    /**
     * 成功率（百分比）
     */
    private double successRate;
    
    /**
     * 获取格式化的统计信息
     */
    public String getFormattedStats() {
        return String.format(
            "网络请求统计 - 总数: %d, 成功: %d, 失败: %d, 成功率: %.2f%%, 平均响应时间: %.2fms",
            totalRequests, successfulRequests, failedRequests, successRate, averageResponseTime
        );
    }
}
