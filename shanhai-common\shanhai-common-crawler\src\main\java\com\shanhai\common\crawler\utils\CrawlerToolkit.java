package com.shanhai.common.crawler.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 爬虫工具包
 * <p>
 * 整合所有爬虫相关的工具方法，提供统一的工具接口
 * 
 * <AUTHOR>
 */
@Slf4j
public class CrawlerToolkit {
    
    // ================== 常量定义 ==================
    
    private static final Pattern URL_PATTERN = Pattern.compile("https?://[^\\s/$.?#].[^\\s]*");
    private static final Pattern DOMAIN_PATTERN = Pattern.compile("https?://([^/]+)");
    private static final Pattern CHAPTER_PATTERN = Pattern.compile(".*[第章节回卷部篇].*|.*Chapter.*|.*\\d+.*");
    
    // 常用User-Agent列表
    private static final List<String> USER_AGENTS = Arrays.asList(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
    );
    
    private static final Random RANDOM = new Random();
    
    // ================== HTML解析工具 ==================
    
    /**
     * 根据CSS选择器提取文本内容
     */
    public static String extractText(Element element, String selector) {
        return extractText(element, selector, null);
    }
    
    /**
     * 根据CSS选择器提取文本或属性值
     */
    public static String extractText(Element element, String selector, String attr) {
        if (element == null || StrUtil.isBlank(selector)) {
            return "";
        }
        
        try {
            Elements elements = element.select(selector);
            if (elements.isEmpty()) {
                return "";
            }
            
            Element target = elements.first();
            String result = StrUtil.isBlank(attr) ? target.text() : target.attr(attr);
            return cleanText(result);
        } catch (Exception e) {
            log.warn("提取文本失败 - selector: {}, attr: {}, error: {}", selector, attr, e.getMessage());
            return "";
        }
    }
    
    /**
     * 提取所有匹配元素的文本
     */
    public static List<String> extractAllText(Element element, String selector) {
        return extractAllText(element, selector, null);
    }
    
    /**
     * 提取所有匹配元素的文本或属性值
     */
    public static List<String> extractAllText(Element element, String selector, String attr) {
        List<String> results = new ArrayList<>();
        
        if (element == null || StrUtil.isBlank(selector)) {
            return results;
        }
        
        try {
            Elements elements = element.select(selector);
            for (Element el : elements) {
                String text = StrUtil.isBlank(attr) ? el.text() : el.attr(attr);
                if (StrUtil.isNotBlank(text)) {
                    results.add(cleanText(text));
                }
            }
        } catch (Exception e) {
            log.warn("批量提取文本失败 - selector: {}, attr: {}, error: {}", selector, attr, e.getMessage());
        }
        
        return results;
    }
    
    /**
     * 检查元素是否存在
     */
    public static boolean hasElement(Document doc, String selector) {
        return doc != null && StrUtil.isNotBlank(selector) && !doc.select(selector).isEmpty();
    }
    
    /**
     * 获取元素数量
     */
    public static int getElementCount(Element element, String selector) {
        if (element == null || StrUtil.isBlank(selector)) {
            return 0;
        }
        return element.select(selector).size();
    }
    
    // ================== 文本处理工具 ==================
    
    /**
     * 清理文本内容
     */
    public static String cleanText(String text) {
        if (StrUtil.isBlank(text)) {
            return "";
        }
        
        return text.replaceAll("\\s+", " ")          // 多个空白字符替换为单个空格
                .replaceAll("&nbsp;", " ")           // HTML空格实体
                .replaceAll("&amp;", "&")            // HTML &实体
                .replaceAll("&lt;", "<")             // HTML <实体
                .replaceAll("&gt;", ">")             // HTML >实体
                .replaceAll("&quot;", "\"")          // HTML "实体
                .replaceAll("&#\\d+;", "")           // 数字实体
                .replaceAll("&[a-zA-Z]+;", "")       // 其他实体
                .trim();
    }
    
    /**
     * 移除HTML标签
     */
    public static String removeHtmlTags(String html) {
        if (StrUtil.isBlank(html)) {
            return "";
        }
        return html.replaceAll("<[^>]+>", "").trim();
    }
    
    /**
     * 清理章节内容
     */
    public static String cleanChapterContent(String content) {
        if (StrUtil.isBlank(content)) {
            return "";
        }
        
        return content
                .replaceAll("(?i)<script[^>]*>.*?</script>", "")  // 移除script标签
                .replaceAll("(?i)<style[^>]*>.*?</style>", "")    // 移除style标签
                .replaceAll("<!--.*?-->", "")                     // 移除注释
                .replaceAll("<[^>]+>", "")                        // 移除所有HTML标签
                .replaceAll("\\s*\\n\\s*", "\n")                 // 清理换行
                .replaceAll("\\n{3,}", "\n\n")                   // 最多保留两个连续换行
                .trim();
    }
    
    /**
     * 检查是否为有效的章节标题
     */
    public static boolean isValidChapterTitle(String title) {
        if (StrUtil.isBlank(title)) {
            return false;
        }
        
        title = title.trim();
        return CHAPTER_PATTERN.matcher(title).matches();
    }
    
    // ================== URL处理工具 ==================
    
    /**
     * 构建完整URL
     */
    public static String buildAbsoluteUrl(String baseUrl, String relativeUrl) {
        if (StrUtil.isBlank(relativeUrl)) {
            return "";
        }
        
        // 如果已经是完整URL，直接返回
        if (isValidUrl(relativeUrl)) {
            return relativeUrl;
        }
        
        if (StrUtil.isBlank(baseUrl)) {
            return relativeUrl;
        }
        
        try {
            // 手动实现URL拼接，因为URLUtil.completeUrl在某些版本中不存在
            if (baseUrl.endsWith("/") && relativeUrl.startsWith("/")) {
                return baseUrl + relativeUrl.substring(1);
            } else if (!baseUrl.endsWith("/") && !relativeUrl.startsWith("/")) {
                return baseUrl + "/" + relativeUrl;
            } else {
                return baseUrl + relativeUrl;
            }
        } catch (Exception e) {
            log.warn("构建完整URL失败 - base: {}, relative: {}, error: {}", baseUrl, relativeUrl, e.getMessage());
            return relativeUrl;
        }
    }
    
    /**
     * 构建搜索URL
     */
    public static String buildSearchUrl(String urlTemplate, String keyword) {
        if (StrUtil.isBlank(urlTemplate) || StrUtil.isBlank(keyword)) {
            return "";
        }
        
        try {
            String encodedKeyword = URLEncoder.encode(keyword, "UTF-8");
            return urlTemplate.replace("{keyword}", encodedKeyword)
                    .replace("{KEYWORD}", encodedKeyword)
                    .replace("${keyword}", encodedKeyword);
        } catch (Exception e) {
            log.warn("构建搜索URL失败 - template: {}, keyword: {}, error: {}", urlTemplate, keyword, e.getMessage());
            return urlTemplate;
        }
    }
    
    /**
     * 验证URL格式
     */
    public static boolean isValidUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        return URL_PATTERN.matcher(url).matches();
    }
    
    /**
     * 从URL中提取域名
     */
    public static String extractDomain(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        
        Matcher matcher = DOMAIN_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
    
    /**
     * 标准化URL（移除fragment和多余参数）
     */
    public static String normalizeUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        
        try {
            // 移除fragment部分
            int fragmentIndex = url.indexOf('#');
            if (fragmentIndex > 0) {
                url = url.substring(0, fragmentIndex);
            }
            
            // 移除尾部斜杠
            if (url.endsWith("/") && url.length() > 1) {
                url = url.substring(0, url.length() - 1);
            }
            
            return url;
        } catch (Exception e) {
            log.warn("标准化URL失败: {}", url, e);
            return url;
        }
    }
    
    // ================== JSON处理工具 ==================
    
    /**
     * 安全解析JSON字符串
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseJsonSafely(String jsonStr) {
        if (StrUtil.isBlank(jsonStr)) {
            return new HashMap<>();
        }

        try {
            return JSONUtil.toBean(jsonStr, Map.class);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", jsonStr, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 从JSON中提取字段值
     */
    public static String extractJsonField(String jsonStr, String fieldPath) {
        if (StrUtil.isBlank(jsonStr) || StrUtil.isBlank(fieldPath)) {
            return "";
        }
        
        try {
            Map<String, Object> jsonMap = parseJsonSafely(jsonStr);
            return extractNestedValue(jsonMap, fieldPath);
        } catch (Exception e) {
            log.warn("提取JSON字段失败 - path: {}, error: {}", fieldPath, e.getMessage());
            return "";
        }
    }
    
    /**
     * 提取嵌套字段值
     */
    @SuppressWarnings("unchecked")
    private static String extractNestedValue(Map<String, Object> map, String path) {
        String[] parts = path.split("\\.");
        Object current = map;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(part);
            } else {
                return "";
            }
            
            if (current == null) {
                return "";
            }
        }
        
        return current.toString();
    }
    
    // ================== 随机工具 ==================
    
    /**
     * 获取随机User-Agent
     */
    public static String getRandomUserAgent() {
        return USER_AGENTS.get(RANDOM.nextInt(USER_AGENTS.size()));
    }
    
    /**
     * 生成随机延迟时间
     */
    public static long getRandomDelay(int minMs, int maxMs) {
        if (minMs >= maxMs) {
            return minMs;
        }
        return minMs + RANDOM.nextInt(maxMs - minMs);
    }
    
    // ================== 验证工具 ==================
    
    /**
     * 验证必需参数
     */
    public static void validateRequired(Object value, String paramName) {
        if (value == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "不能为空");
        }
        
        if (value instanceof String && StrUtil.isBlank((String) value)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "不能为空");
        }
    }
    
    /**
     * 验证URL参数
     */
    public static void validateUrl(String url, String paramName) {
        validateRequired(url, paramName);
        if (!isValidUrl(url)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "格式无效: " + url);
        }
    }
    
    // ================== 其他工具 ==================
    
    /**
     * 生成唯一标识符
     */
    public static String generateId(String... parts) {
        if (parts == null || parts.length == 0) {
            return "unknown_" + System.currentTimeMillis();
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            if (i > 0) {
                sb.append("_");
            }
            String part = StrUtil.isBlank(parts[i]) ? "unknown" : parts[i];
            sb.append(part.replaceAll("[^a-zA-Z0-9_\\u4e00-\\u9fa5]", "_"));
        }
        
        return sb.toString().replaceAll("_{2,}", "_");
    }
    
    /**
     * 安全休眠
     */
    public static void safeSleep(long millis) {
        if (millis <= 0) {
            return;
        }
        
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("休眠被中断");
        }
    }
    
    /**
     * 获取当前时间戳
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        
        String[] units = {"KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    // 私有构造函数，防止实例化
    private CrawlerToolkit() {
        throw new UnsupportedOperationException("工具类不能实例化");
    }
}
