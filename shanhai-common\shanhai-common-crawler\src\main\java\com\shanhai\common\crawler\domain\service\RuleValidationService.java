package com.shanhai.common.crawler.domain.service;

import com.shanhai.common.crawler.domain.aggregate.CrawlerRule;
import com.shanhai.common.crawler.domain.entity.rule.*;
import com.shanhai.common.crawler.domain.valueobject.CrawlerMode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * 规则验证领域服务
 * <p>
 * 提供爬虫规则配置的验证功能，确保规则配置的完整性、正确性和一致性。
 * 作为领域服务，包含复杂的业务验证逻辑。
 * 
 * <p>主要职责：
 * <ul>
 *   <li>验证规则配置的完整性</li>
 *   <li>检查配置项的格式和有效性</li>
 *   <li>验证配置之间的一致性</li>
 *   <li>提供详细的验证错误信息</li>
 * </ul>
 * 
 * <p>验证范围：
 * <ul>
 *   <li>基本信息验证：数据源信息、采集模式等</li>
 *   <li>子配置验证：搜索、书籍信息、章节、内容配置</li>
 *   <li>关联关系验证：配置之间的依赖关系</li>
 *   <li>业务规则验证：特定业务场景的规则</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class RuleValidationService {

    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$"
    );
    
    private static final Pattern IDENTIFIER_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_-]{2,50}$"
    );

    // ==================== 主要验证方法 ====================

    /**
     * 验证爬虫规则配置的完整性和正确性
     * 
     * @param rule 爬虫规则
     * @return 验证是否通过
     * @throws CrawlerException 验证失败时抛出异常
     */
    public boolean validateRuleConfiguration(CrawlerRule rule) {
        log.debug("开始验证爬虫规则配置: {}", rule != null ? rule.getSourceIdentifier() : "null");
        
        List<String> errors = new ArrayList<>();
        
        try {
            // 验证基本信息
            validateBasicInfo(rule, errors);
            
            // 验证子配置
            validateSubConfigurations(rule, errors);
            
            // 验证配置一致性
            validateConfigurationConsistency(rule, errors);
            
            // 如果有错误，抛出异常
            if (!errors.isEmpty()) {
                String errorMessage = "规则配置验证失败:\n" + String.join("\n", errors);
                throw new CrawlerException(CrawlerErrorCode.RULE_VALIDATION_FAILED, errorMessage);
            }
            
            log.debug("爬虫规则配置验证通过: {}", rule.getSourceIdentifier());
            return true;
        } catch (CrawlerException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证爬虫规则配置时发生异常: {}", rule != null ? rule.getSourceIdentifier() : "null", e);
            throw new CrawlerException(CrawlerErrorCode.SYSTEM_ERROR, "验证过程中发生异常: " + e.getMessage(), e);
        }
    }

    // ==================== 基本信息验证 ====================

    /**
     * 验证基本信息
     * 
     * @param rule 爬虫规则
     * @param errors 错误列表
     */
    private void validateBasicInfo(CrawlerRule rule, List<String> errors) {
        if (rule == null) {
            errors.add("爬虫规则不能为空");
            return;
        }
        
        // 验证数据源信息
        if (rule.getSourceInfo() == null) {
            errors.add("数据源信息不能为空");
        } else {
            if (!rule.getSourceInfo().isValid()) {
                errors.add("数据源信息格式不正确");
            }
        }
        
        // 验证数据源标识符
        if (rule.getSourceIdentifier() == null || rule.getSourceIdentifier().trim().isEmpty()) {
            errors.add("数据源标识符不能为空");
        } else if (!IDENTIFIER_PATTERN.matcher(rule.getSourceIdentifier()).matches()) {
            errors.add("数据源标识符格式不正确，只能包含字母、数字、下划线、连字符，长度2-50字符");
        }
        
        // 验证数据源名称
        if (rule.getSourceName() == null || rule.getSourceName().trim().isEmpty()) {
            errors.add("数据源名称不能为空");
        } else if (rule.getSourceName().trim().length() > 100) {
            errors.add("数据源名称长度不能超过100字符");
        }
        
        // 验证基础URL
        if (rule.getSourceBaseUrl() == null || rule.getSourceBaseUrl().trim().isEmpty()) {
            errors.add("数据源基础URL不能为空");
        } else if (!URL_PATTERN.matcher(rule.getSourceBaseUrl()).matches()) {
            errors.add("数据源基础URL格式不正确");
        }
        
        // 验证采集模式
        if (rule.getCrawlerMode() == null) {
            errors.add("采集模式不能为空");
        }
        
        // 验证超时时间
        if (rule.getTimeoutMillis() != null && rule.getTimeoutMillis() <= 0) {
            errors.add("超时时间必须大于0");
        }
    }

    // ==================== 子配置验证 ====================

    /**
     * 验证子配置
     * 
     * @param rule 爬虫规则
     * @param errors 错误列表
     */
    private void validateSubConfigurations(CrawlerRule rule, List<String> errors) {
        if (rule == null) {
            return;
        }
        
        // 验证搜索规则配置
        validateSearchRuleConfig(rule.getSearchRuleConfig(), errors);
        
        // 验证书籍信息规则配置
        validateBookInfoRuleConfig(rule.getBookInfoRuleConfig(), errors);
        
        // 验证章节规则配置
        validateChapterRuleConfig(rule.getChapterRuleConfig(), errors);
        
        // 验证内容规则配置
        validateContentRuleConfig(rule.getContentRuleConfig(), errors);
        
        // 验证反爬虫配置（可选）
        if (rule.getAntiSpiderConfig() != null) {
            validateAntiSpiderConfig(rule.getAntiSpiderConfig(), errors);
        }
    }

    /**
     * 验证搜索规则配置
     * 
     * @param config 搜索规则配置
     * @param errors 错误列表
     */
    private void validateSearchRuleConfig(SearchRuleConfig config, List<String> errors) {
        if (config == null) {
            errors.add("搜索规则配置不能为空");
            return;
        }
        
        if (!config.isValid()) {
            errors.add("搜索规则配置不完整");
        }
        
        // 验证搜索URL模板
        if (config.getSearchUrlTemplate() == null || config.getSearchUrlTemplate().trim().isEmpty()) {
            errors.add("搜索URL模板不能为空");
        } else if (!config.getSearchUrlTemplate().contains("{keyword}") && 
                   !config.getSearchUrlTemplate().contains("{" + config.getKeywordParamName() + "}")) {
            errors.add("搜索URL模板必须包含关键词占位符");
        }
        
        // 验证HTTP方法
        if (config.getHttpMethod() != null && 
            !config.getHttpMethod().matches("^(GET|POST|PUT|DELETE)$")) {
            errors.add("HTTP方法只能是GET、POST、PUT、DELETE之一");
        }
        
        // 验证分页配置
        if (Boolean.TRUE.equals(config.getPaginationEnabled())) {
            if (config.getMaxPageLimit() != null && config.getMaxPageLimit() <= 0) {
                errors.add("最大页数限制必须大于0");
            }
        }
        
        // 验证正则表达式
        if (config.getResultFilterRegex() != null && !config.getResultFilterRegex().trim().isEmpty()) {
            try {
                Pattern.compile(config.getResultFilterRegex());
            } catch (PatternSyntaxException e) {
                errors.add("搜索结果过滤正则表达式格式不正确: " + e.getMessage());
            }
        }
    }

    /**
     * 验证书籍信息规则配置
     * 
     * @param config 书籍信息规则配置
     * @param errors 错误列表
     */
    private void validateBookInfoRuleConfig(BookInfoRuleConfig config, List<String> errors) {
        if (config == null) {
            errors.add("书籍信息规则配置不能为空");
            return;
        }
        
        if (!config.isValid()) {
            errors.add("书籍信息规则配置不完整");
        }
        
        // 验证必需的选择器
        if (config.getBookTitleSelector() == null || config.getBookTitleSelector().trim().isEmpty()) {
            errors.add("书名选择器不能为空");
        }
        
        // 验证正则表达式
        if (config.getIntroFilterRegex() != null && !config.getIntroFilterRegex().trim().isEmpty()) {
            try {
                Pattern.compile(config.getIntroFilterRegex());
            } catch (PatternSyntaxException e) {
                errors.add("简介过滤正则表达式格式不正确: " + e.getMessage());
            }
        }
        
        // 验证文本长度限制
        if (config.getTextLengthLimit() != null && config.getTextLengthLimit() <= 0) {
            errors.add("文本长度限制必须大于0");
        }
    }

    /**
     * 验证章节规则配置
     * 
     * @param config 章节规则配置
     * @param errors 错误列表
     */
    private void validateChapterRuleConfig(ChapterRuleConfig config, List<String> errors) {
        if (config == null) {
            errors.add("章节规则配置不能为空");
            return;
        }
        
        if (!config.isValid()) {
            errors.add("章节规则配置不完整");
        }
        
        // 验证必需的选择器
        if (config.getChapterListContainerSelector() == null || config.getChapterListContainerSelector().trim().isEmpty()) {
            errors.add("章节列表容器选择器不能为空");
        }
        
        if (config.getChapterItemSelector() == null || config.getChapterItemSelector().trim().isEmpty()) {
            errors.add("章节项选择器不能为空");
        }
        
        // 验证分页配置
        if (Boolean.TRUE.equals(config.getPaginationEnabled())) {
            if (config.getMaxPageLimit() != null && config.getMaxPageLimit() <= 0) {
                errors.add("章节列表最大页数限制必须大于0");
            }
        }
        
        // 验证正则表达式
        if (config.getChapterFilterRegex() != null && !config.getChapterFilterRegex().trim().isEmpty()) {
            try {
                Pattern.compile(config.getChapterFilterRegex());
            } catch (PatternSyntaxException e) {
                errors.add("章节过滤正则表达式格式不正确: " + e.getMessage());
            }
        }
        
        // 验证标题长度限制
        if (config.getMinTitleLength() != null && config.getMaxTitleLength() != null &&
            config.getMinTitleLength() > config.getMaxTitleLength()) {
            errors.add("章节标题最小长度不能大于最大长度");
        }
    }

    /**
     * 验证内容规则配置
     * 
     * @param config 内容规则配置
     * @param errors 错误列表
     */
    private void validateContentRuleConfig(ContentRuleConfig config, List<String> errors) {
        if (config == null) {
            errors.add("内容规则配置不能为空");
            return;
        }
        
        if (!config.isValid()) {
            errors.add("内容规则配置不完整");
        }
        
        // 验证必需的选择器
        if (config.getContentSelector() == null || config.getContentSelector().trim().isEmpty()) {
            errors.add("正文内容选择器不能为空");
        }
        
        // 验证分页配置
        if (Boolean.TRUE.equals(config.getPaginationEnabled())) {
            if (config.getMaxPageLimit() != null && config.getMaxPageLimit() <= 0) {
                errors.add("内容页最大页数限制必须大于0");
            }
        }
        
        // 验证正则表达式
        if (config.getContentFilterRegex() != null && !config.getContentFilterRegex().trim().isEmpty()) {
            try {
                Pattern.compile(config.getContentFilterRegex());
            } catch (PatternSyntaxException e) {
                errors.add("内容过滤正则表达式格式不正确: " + e.getMessage());
            }
        }
        
        // 验证内容长度限制
        if (config.getMinContentLength() != null && config.getMaxContentLength() != null &&
            config.getMinContentLength() > config.getMaxContentLength()) {
            errors.add("内容最小长度不能大于最大长度");
        }
        
        // 验证重复内容阈值
        if (config.getDuplicateThreshold() != null && 
            (config.getDuplicateThreshold() < 0.0 || config.getDuplicateThreshold() > 1.0)) {
            errors.add("重复内容阈值必须在0.0到1.0之间");
        }
    }

    /**
     * 验证反爬虫配置
     * 
     * @param config 反爬虫配置
     * @param errors 错误列表
     */
    private void validateAntiSpiderConfig(AntiSpiderConfig config, List<String> errors) {
        if (config == null) {
            return;
        }
        
        // 验证延时配置
        if (config.getMinDelayMillis() != null && config.getMaxDelayMillis() != null &&
            config.getMinDelayMillis() > config.getMaxDelayMillis()) {
            errors.add("最小延时不能大于最大延时");
        }
        
        // 验证重试次数
        if (config.getMaxRetryCount() != null && config.getMaxRetryCount() < 0) {
            errors.add("最大重试次数不能小于0");
        }
        
        // 验证超时时间
        if (config.getConnectTimeoutMillis() != null && config.getConnectTimeoutMillis() <= 0) {
            errors.add("连接超时时间必须大于0");
        }
        
        if (config.getReadTimeoutMillis() != null && config.getReadTimeoutMillis() <= 0) {
            errors.add("读取超时时间必须大于0");
        }
    }

    // ==================== 配置一致性验证 ====================

    /**
     * 验证配置一致性
     * 
     * @param rule 爬虫规则
     * @param errors 错误列表
     */
    private void validateConfigurationConsistency(CrawlerRule rule, List<String> errors) {
        if (rule == null) {
            return;
        }
        
        // 验证采集模式与配置的一致性
        validateModeConsistency(rule, errors);
        
        // 验证URL配置的一致性
        validateUrlConsistency(rule, errors);
        
        // 验证分页配置的一致性
        validatePaginationConsistency(rule, errors);
    }

    /**
     * 验证采集模式与配置的一致性
     * 
     * @param rule 爬虫规则
     * @param errors 错误列表
     */
    private void validateModeConsistency(CrawlerRule rule, List<String> errors) {
        CrawlerMode mode = rule.getCrawlerMode();
        if (mode == null) {
            return;
        }
        
        // API模式需要特殊的配置
        if (mode.isApiMode()) {
            // API模式的搜索URL应该是API端点
            SearchRuleConfig searchConfig = rule.getSearchRuleConfig();
            if (searchConfig != null && searchConfig.getSearchUrlTemplate() != null) {
                String url = searchConfig.getSearchUrlTemplate().toLowerCase();
                if (!url.contains("api") && !url.contains("json")) {
                    errors.add("API模式的搜索URL应该是API端点");
                }
            }
        }
        
        // Selenium模式需要JavaScript支持
        if (mode.isSeleniumMode()) {
            AntiSpiderConfig antiSpiderConfig = rule.getAntiSpiderConfig();
            if (antiSpiderConfig != null && !Boolean.TRUE.equals(antiSpiderConfig.getJavascriptEnabled())) {
                errors.add("Selenium模式需要启用JavaScript支持");
            }
        }
    }

    /**
     * 验证URL配置的一致性
     * 
     * @param rule 爬虫规则
     * @param errors 错误列表
     */
    private void validateUrlConsistency(CrawlerRule rule, List<String> errors) {
        String baseUrl = rule.getSourceBaseUrl();
        if (baseUrl == null) {
            return;
        }
        
        // 提取域名
        String domain = extractDomain(baseUrl);
        if (domain == null) {
            return;
        }
        
        // 验证搜索URL是否与基础URL在同一域名下
        SearchRuleConfig searchConfig = rule.getSearchRuleConfig();
        if (searchConfig != null && searchConfig.getSearchUrlTemplate() != null) {
            String searchDomain = extractDomain(searchConfig.getSearchUrlTemplate());
            if (searchDomain != null && !searchDomain.equals(domain)) {
                errors.add("搜索URL与基础URL不在同一域名下");
            }
        }
    }

    /**
     * 验证分页配置的一致性
     * 
     * @param rule 爬虫规则
     * @param errors 错误列表
     */
    private void validatePaginationConsistency(CrawlerRule rule, List<String> errors) {
        // 检查各个配置的分页设置是否合理
        SearchRuleConfig searchConfig = rule.getSearchRuleConfig();
        ChapterRuleConfig chapterConfig = rule.getChapterRuleConfig();
        ContentRuleConfig contentConfig = rule.getContentRuleConfig();
        
        // 如果启用了分页，应该有相应的选择器
        if (searchConfig != null && Boolean.TRUE.equals(searchConfig.getPaginationEnabled())) {
            if (searchConfig.getNextPageSelector() == null || searchConfig.getNextPageSelector().trim().isEmpty()) {
                errors.add("搜索配置启用了分页但未配置下一页选择器");
            }
        }
        
        if (chapterConfig != null && Boolean.TRUE.equals(chapterConfig.getPaginationEnabled())) {
            if (chapterConfig.getNextPageSelector() == null || chapterConfig.getNextPageSelector().trim().isEmpty()) {
                errors.add("章节配置启用了分页但未配置下一页选择器");
            }
        }
        
        if (contentConfig != null && Boolean.TRUE.equals(contentConfig.getPaginationEnabled())) {
            if (contentConfig.getNextPageSelector() == null || contentConfig.getNextPageSelector().trim().isEmpty()) {
                errors.add("内容配置启用了分页但未配置下一页选择器");
            }
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 从URL中提取域名
     * 
     * @param url URL字符串
     * @return 域名，如果提取失败返回null
     */
    private String extractDomain(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        
        try {
            String cleanUrl = url.trim().toLowerCase();
            if (cleanUrl.startsWith("http://")) {
                cleanUrl = cleanUrl.substring(7);
            } else if (cleanUrl.startsWith("https://")) {
                cleanUrl = cleanUrl.substring(8);
            }
            
            int slashIndex = cleanUrl.indexOf('/');
            if (slashIndex > 0) {
                cleanUrl = cleanUrl.substring(0, slashIndex);
            }
            
            int colonIndex = cleanUrl.indexOf(':');
            if (colonIndex > 0) {
                cleanUrl = cleanUrl.substring(0, colonIndex);
            }
            
            return cleanUrl;
        } catch (Exception e) {
            log.warn("提取域名失败: {}", url, e);
            return null;
        }
    }
}
