package com.shanhai.common.crawler.domain.valueobject;

import lombok.Getter;

/**
 * 爬虫采集模式枚举
 * <p>
 * 定义爬虫支持的各种采集模式，每种模式有不同的特点和适用场景。
 * 
 * <p>支持的采集模式：
 * <ul>
 *   <li>HTML - 传统HTML页面解析，适用于静态网页</li>
 *   <li>API - RESTful API接口调用，适用于提供API的站点</li>
 *   <li>SELENIUM - 浏览器自动化，适用于JavaScript渲染的动态页面</li>
 *   <li>AUTO - 自动检测模式，根据站点特征自动选择最佳采集方式</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Getter
public enum CrawlerMode {

    /**
     * HTML模式
     * 使用传统的HTTP请求获取HTML页面，然后使用CSS选择器或XPath解析内容
     * 
     * <p>特点：
     * <ul>
     *   <li>性能高，资源消耗少</li>
     *   <li>适用于静态HTML页面</li>
     *   <li>不支持JavaScript渲染</li>
     *   <li>反爬虫检测相对容易</li>
     * </ul>
     * 
     * <p>适用场景：
     * <ul>
     *   <li>传统的服务器端渲染网站</li>
     *   <li>内容完全在HTML中的页面</li>
     *   <li>对性能要求较高的场景</li>
     * </ul>
     */
    HTML("HTML", "HTML页面解析", "使用HTTP请求获取HTML页面并解析", true, false),

    /**
     * API模式
     * 直接调用网站提供的RESTful API接口获取结构化数据
     * 
     * <p>特点：
     * <ul>
     *   <li>数据结构化，解析简单</li>
     *   <li>性能最高，网络开销最小</li>
     *   <li>稳定性好，不易受页面改版影响</li>
     *   <li>需要网站提供公开API</li>
     * </ul>
     * 
     * <p>适用场景：
     * <ul>
     *   <li>提供公开API的网站</li>
     *   <li>移动应用的后端API</li>
     *   <li>对数据准确性要求高的场景</li>
     * </ul>
     */
    API("API", "API接口调用", "调用RESTful API接口获取结构化数据", true, false),

    /**
     * Selenium模式
     * 使用浏览器自动化工具模拟真实用户操作，支持JavaScript渲染
     * 
     * <p>特点：
     * <ul>
     *   <li>完全模拟真实浏览器行为</li>
     *   <li>支持JavaScript渲染和AJAX请求</li>
     *   <li>可以处理复杂的用户交互</li>
     *   <li>资源消耗大，性能相对较低</li>
     * </ul>
     * 
     * <p>适用场景：
     * <ul>
     *   <li>大量使用JavaScript的SPA应用</li>
     *   <li>需要用户交互的页面（如登录、点击等）</li>
     *   <li>内容通过AJAX动态加载的页面</li>
     * </ul>
     */
    SELENIUM("SELENIUM", "浏览器自动化", "使用Selenium模拟浏览器操作，支持JavaScript", false, true),

    /**
     * 自动检测模式
     * 根据网站特征自动选择最适合的采集模式
     * 
     * <p>检测策略：
     * <ul>
     *   <li>首先尝试检测是否有可用的API接口</li>
     *   <li>然后检测页面是否需要JavaScript渲染</li>
     *   <li>最后根据检测结果选择最佳模式</li>
     * </ul>
     * 
     * <p>适用场景：
     * <ul>
     *   <li>不确定网站技术架构的情况</li>
     *   <li>需要处理多种类型网站的通用爬虫</li>
     *   <li>希望自动优化采集策略的场景</li>
     * </ul>
     */
    AUTO("AUTO", "自动检测", "根据网站特征自动选择最佳采集模式", true, true);

    /**
     * 模式代码
     */
    private final String code;

    /**
     * 模式名称
     */
    private final String name;

    /**
     * 模式描述
     */
    private final String description;

    /**
     * 是否支持高性能采集
     */
    private final boolean highPerformance;

    /**
     * 是否支持JavaScript渲染
     */
    private final boolean javascriptSupport;

    /**
     * 构造函数
     * 
     * @param code 模式代码
     * @param name 模式名称
     * @param description 模式描述
     * @param highPerformance 是否支持高性能采集
     * @param javascriptSupport 是否支持JavaScript渲染
     */
    CrawlerMode(String code, String name, String description, boolean highPerformance, boolean javascriptSupport) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.highPerformance = highPerformance;
        this.javascriptSupport = javascriptSupport;
    }

    // ==================== 业务方法 ====================

    /**
     * 根据代码获取采集模式
     * 
     * @param code 模式代码
     * @return 采集模式，如果未找到返回null
     */
    public static CrawlerMode fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        String upperCode = code.trim().toUpperCase();
        for (CrawlerMode mode : values()) {
            if (mode.code.equals(upperCode)) {
                return mode;
            }
        }
        return null;
    }

    /**
     * 根据代码获取采集模式，如果未找到返回默认值
     * 
     * @param code 模式代码
     * @param defaultMode 默认模式
     * @return 采集模式
     */
    public static CrawlerMode fromCodeOrDefault(String code, CrawlerMode defaultMode) {
        CrawlerMode mode = fromCode(code);
        return mode != null ? mode : defaultMode;
    }

    /**
     * 检查是否为高性能模式
     * 
     * @return 是否为高性能模式
     */
    public boolean isHighPerformance() {
        return highPerformance;
    }

    /**
     * 检查是否支持JavaScript
     * 
     * @return 是否支持JavaScript
     */
    public boolean supportsJavaScript() {
        return javascriptSupport;
    }

    /**
     * 检查是否为API模式
     * 
     * @return 是否为API模式
     */
    public boolean isApiMode() {
        return this == API;
    }

    /**
     * 检查是否为HTML模式
     * 
     * @return 是否为HTML模式
     */
    public boolean isHtmlMode() {
        return this == HTML;
    }

    /**
     * 检查是否为Selenium模式
     * 
     * @return 是否为Selenium模式
     */
    public boolean isSeleniumMode() {
        return this == SELENIUM;
    }

    /**
     * 检查是否为自动检测模式
     * 
     * @return 是否为自动检测模式
     */
    public boolean isAutoMode() {
        return this == AUTO;
    }

    /**
     * 获取推荐的超时时间（毫秒）
     * 不同模式有不同的推荐超时时间
     * 
     * @return 推荐超时时间
     */
    public long getRecommendedTimeoutMillis() {
        switch (this) {
            case API:
                return 10000L; // API模式：10秒
            case HTML:
                return 30000L; // HTML模式：30秒
            case SELENIUM:
                return 60000L; // Selenium模式：60秒
            case AUTO:
                return 45000L; // 自动模式：45秒
            default:
                return 30000L; // 默认：30秒
        }
    }

    /**
     * 获取推荐的重试次数
     * 不同模式有不同的推荐重试次数
     * 
     * @return 推荐重试次数
     */
    public int getRecommendedRetryCount() {
        switch (this) {
            case API:
                return 3; // API模式：3次
            case HTML:
                return 3; // HTML模式：3次
            case SELENIUM:
                return 2; // Selenium模式：2次（资源消耗大）
            case AUTO:
                return 3; // 自动模式：3次
            default:
                return 3; // 默认：3次
        }
    }

    /**
     * 获取模式的优先级
     * 数值越小优先级越高，用于自动选择模式时的排序
     * 
     * @return 优先级（1-4）
     */
    public int getPriority() {
        switch (this) {
            case API:
                return 1; // 最高优先级
            case HTML:
                return 2; // 高优先级
            case AUTO:
                return 3; // 中等优先级
            case SELENIUM:
                return 4; // 最低优先级（资源消耗大）
            default:
                return 3;
        }
    }

    /**
     * 检查模式是否兼容指定的网站特征
     * 
     * @param requiresJavaScript 是否需要JavaScript支持
     * @param hasApiEndpoint 是否有API端点
     * @return 是否兼容
     */
    public boolean isCompatibleWith(boolean requiresJavaScript, boolean hasApiEndpoint) {
        switch (this) {
            case API:
                return hasApiEndpoint;
            case HTML:
                return !requiresJavaScript;
            case SELENIUM:
                return true; // Selenium支持所有场景
            case AUTO:
                return true; // 自动模式支持所有场景
            default:
                return false;
        }
    }

    @Override
    public String toString() {
        return name + "(" + code + ")";
    }
}
