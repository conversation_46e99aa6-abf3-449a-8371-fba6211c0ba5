package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import java.io.Serializable;

/**
 * 内容替换规则
 * <p>
 * 用于对采集到的内容进行正则替换处理。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_replace_rule", comment = "内容替换规则表")
@TableName("crawler_replace_rule")
public class CrawlerRuleReplace implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /** 匹配正则表达式 */
    @Column(value = "pattern", comment = "匹配正则表达式", type = "TEXT")
    private String pattern;

    /** 替换内容 */
    @Column(value = "replacement", comment = "替换内容", type = "TEXT")
    private String replacement;

    /** 规则名称 */
    @Column(value = "rule_name", comment = "规则名称", length = 100)
    private String ruleName;

    /** 规则描述 */
    @Column(value = "rule_description", comment = "规则描述", length = 500)
    private String ruleDescription;

    /** 是否启用 */
    @Builder.Default
    @Column(value = "enabled", comment = "是否启用")
    private Boolean enabled = true;

    /** 排序 */
    @Builder.Default
    @Column(value = "sort_order", comment = "排序")
    private Integer sortOrder = 0;

    @Column(value = "rule_id", comment = "关联规则ID", length = 32)
    private String ruleId;
} 