package com.shanhai.api.controller;

import com.shanhai.common.core.result.BaseController;
import com.shanhai.common.core.result.Result;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.service.NovelCrawlerRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 爬虫规则管理控制器
 * <p>
 * 提供爬虫规则的REST API接口，包括：
 * <ul>
 *   <li>规则的增删改查操作</li>
 *   <li>规则状态管理</li>
 *   <li>规则验证功能</li>
 *   <li>批量操作支持</li>
 * </ul>
 *
 * <p>设计原则：
 * <ul>
 *   <li>RESTful API设计</li>
 *   <li>统一的响应格式</li>
 *   <li>完善的异常处理</li>
 *   <li>详细的API文档</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024-01-01
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/crawler/rules")
public class NovelCrawlerRuleController extends BaseController {

    @Resource
    private NovelCrawlerRuleService crawlerRuleService;

    /**
     * 查询所有爬虫规则
     *
     * @return 规则列表
     */
    @GetMapping
    public Result listRules() {
        try {
            List<CrawlerRuleNovel> rules = crawlerRuleService.loadAllRules();
            log.info("查询爬虫规则成功，共{}条", rules.size());
            return success(rules);
        } catch (Exception e) {
            log.error("查询爬虫规则失败", e);
            return error("查询爬虫规则失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询单个爬虫规则
     *
     * @param id 规则唯一标识（如 sourceId）
     * @return 规则详情
     */
    @GetMapping("/{id}")
    public Result getRuleById(
            @PathVariable @NotBlank(message = "规则ID不能为空") String id) {
        try {
            CrawlerRuleNovel rule = crawlerRuleService.loadRuleById(id);
            if (rule == null) {
                log.warn("未找到ID为{}的爬虫规则", id);
                return error("未找到指定的爬虫规则");
            }
            log.info("查询爬虫规则成功，ID: {}", id);
            return success(rule);
        } catch (Exception e) {
            log.error("查询爬虫规则失败，ID: {}", id, e);
            return error("查询爬虫规则失败: " + e.getMessage());
        }
    }

    /**
     * 创建新的爬虫规则
     *
     * @param rule 规则对象
     * @return 创建结果
     */
    @PostMapping
    public Result createRule(@Valid @RequestBody CrawlerRuleNovel rule) {
        try {
            // 验证规则的必要字段
            if (rule.getSourceName() == null || rule.getSourceName().trim().isEmpty()) {
                return error("规则源名称不能为空");
            }
            if (rule.getSourceUrl() == null || rule.getSourceUrl().trim().isEmpty()) {
                return error("规则源URL不能为空");
            }

            boolean result = crawlerRuleService.saveRule(rule);
            if (result) {
                log.info("创建爬虫规则成功，源名称: {}", rule.getSourceName());
                return success("创建爬虫规则成功");
            } else {
                log.warn("创建爬虫规则失败，源名称: {}", rule.getSourceName());
                return error("创建爬虫规则失败");
            }
        } catch (Exception e) {
            log.error("创建爬虫规则失败", e);
            return error("创建爬虫规则失败: " + e.getMessage());
        }
    }

    /**
     * 更新聚合爬虫规则
     *
     * @param rule 聚合规则对象
     * @return 是否成功
     */
    @PutMapping("/{id}")
    public Result updateRule(
            @PathVariable @NotBlank(message = "规则ID不能为空") String id,
            @Valid @RequestBody CrawlerRuleNovel rule) {
        try {
            boolean result = crawlerRuleService.updateRule(rule);
            if (result) {
                log.info("更新爬虫规则成功，ID: {}", id);
                return success("更新爬虫规则成功");
            } else {
                log.warn("更新爬虫规则失败，ID: {}", id);
                return error("更新爬虫规则失败");
            }
        } catch (Exception e) {
            log.error("更新爬虫规则失败，ID: {}", id, e);
            return error("更新爬虫规则失败: " + e.getMessage());
        }
    }

    /**
     * 删除爬虫规则
     *
     * @param id 规则ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result deleteRule(
            @PathVariable @NotBlank(message = "规则ID不能为空") String id) {
        try {
            boolean result = crawlerRuleService.deleteRuleById(id);
            if (result) {
                log.info("删除爬虫规则成功，ID: {}", id);
                return success("删除爬虫规则成功");
            } else {
                log.warn("删除爬虫规则失败，ID: {}", id);
                return error("删除爬虫规则失败");
            }
        } catch (Exception e) {
            log.error("删除爬虫规则失败，ID: {}", id, e);
            return error("删除爬虫规则失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入爬虫规则
     *
     * @param rules 规则列表
     * @return 导入结果
     */
    @PostMapping("/batch")
    public Result importRules(@Valid @RequestBody List<CrawlerRuleNovel> rules) {
        try {
            if (rules == null || rules.isEmpty()) {
                return error("导入规则列表不能为空");
            }

            boolean result = crawlerRuleService.importRules(rules);
            if (result) {
                log.info("批量导入爬虫规则成功，共{}条", rules.size());
                return success("批量导入爬虫规则成功");
            } else {
                log.warn("批量导入爬虫规则失败");
                return error("批量导入爬虫规则失败");
            }
        } catch (Exception e) {
            log.error("批量导入爬虫规则失败", e);
            return error("批量导入爬虫规则失败: " + e.getMessage());
        }
    }

    /**
     * 导出所有爬虫规则
     *
     * @return 规则列表
     */
    @GetMapping("/export")
    public Result exportRules() {
        try {
            List<CrawlerRuleNovel> rules = crawlerRuleService.exportRules();
            log.info("导出爬虫规则成功，共{}条", rules.size());
            return success(rules);
        } catch (Exception e) {
            log.error("导出爬虫规则失败", e);
            return error("导出爬虫规则失败: " + e.getMessage());
        }
    }
} 