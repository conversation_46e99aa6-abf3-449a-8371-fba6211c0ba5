# 山海小说爬虫 API

## 启动应用

1. 确保已安装 Java 8+ 和 Maven
2. 在项目根目录执行：
   ```bash
   mvn clean install
   cd shanhai-api
   mvn spring-boot:run
   ```

3. 应用将在 `http://localhost:8200` 启动

## API 接口

### 搜索小说
```bash
curl -X POST http://localhost:8200/api/v1/novel/crawler/search \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "sourceInfo": {
        "sourceName": "笔趣阁",
        "sourceUrl": "https://www.bqg128.com/",
        "timeout": 5000,
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      },
      "searchConfig": {
        "searchUrl": "https://www.bqg128.com/modules/article/search.php?searchkey={keyword}",
        "searchParam": "keyword",
        "bookListSelector": ".result-list .result-item",
        "bookItem": {
          "nameSelector": ".result-game-item-title-link",
          "authorSelector": ".result-game-item-info-tag span",
          "coverSelector": ".result-game-item-pic img",
          "introSelector": ".result-game-item-desc",
          "categorySelector": ".result-game-item-info-tag span",
          "bookUrlSelector": ".result-game-item-title-link",
          "bookUrlAttr": "href"
        }
      }
    },
    "keyword": "斗罗大陆"
  }'
```

## 常见问题

### 404 错误
如果遇到 404 错误，请检查：

1. 应用是否正常启动
2. 端口是否正确（默认 8200）
3. 核心业务路由是否正确注册

### 启动日志
启动时应该看到类似以下日志：
```
✓ 核心搜索路由已注册: /api/v1/novel/crawler/search
```

## API 文档

### 搜索书籍
- **URL**: `POST /api/v1/novel/crawler/search`
- **描述**: 根据关键词搜索小说书籍

### 获取书籍详情
- **URL**: `POST /api/v1/novel/crawler/book/info`
- **描述**: 根据书籍URL获取书籍详细信息

### 获取章节列表
- **URL**: `POST /api/v1/novel/crawler/chapter/list`
- **描述**: 根据章节列表URL获取书籍的所有章节信息

### 获取章节内容
- **URL**: `POST /api/v1/novel/crawler/chapter/content`
- **描述**: 根据章节URL获取章节的具体内容

### 批量获取章节内容
- **URL**: `POST /api/v1/novel/crawler/chapter/batch-content`
- **描述**: 根据章节URL列表批量获取章节内容

### 获取爬虫状态
- **URL**: `GET /api/v1/novel/crawler/status`
- **描述**: 返回当前爬虫服务的运行状态信息 