package com.shanhai.common.crawler.application.service;

import com.shanhai.common.crawler.domain.aggregate.CrawlerRule;
import com.shanhai.common.crawler.domain.repository.CrawlerRuleRepository;
import com.shanhai.common.crawler.domain.service.RuleValidationService;
import com.shanhai.common.crawler.domain.valueobject.RuleStatus;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 爬虫规则应用服务
 * <p>
 * 提供爬虫规则的应用层业务逻辑处理，包括规则的增删改查、验证、状态管理等功能。
 * 作为应用层服务，协调领域服务和基础设施层，实现完整的业务流程。
 * 
 * <p>主要职责：
 * <ul>
 *   <li>规则的完整生命周期管理</li>
 *   <li>业务流程的编排和协调</li>
 *   <li>事务边界的控制</li>
 *   <li>异常处理和错误转换</li>
 * </ul>
 * 
 * <p>设计原则：
 * <ul>
 *   <li>应用服务不包含业务逻辑，只负责流程编排</li>
 *   <li>通过领域服务实现具体的业务规则</li>
 *   <li>确保事务的一致性和完整性</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrawlerRuleApplicationService {

    private final CrawlerRuleRepository crawlerRuleRepository;
    private final RuleValidationService ruleValidationService;

    // ==================== 规则查询操作 ====================

    /**
     * 查找所有爬虫规则
     * 
     * @return 规则列表
     */
    public List<CrawlerRule> findAllRules() {
        log.debug("查找所有爬虫规则");
        try {
            return crawlerRuleRepository.findAll();
        } catch (Exception e) {
            log.error("查找所有爬虫规则失败", e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "查找规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据ID查找爬虫规则
     * 
     * @param ruleId 规则ID
     * @return 爬虫规则，如果不存在返回空
     */
    public Optional<CrawlerRule> findRuleById(String ruleId) {
        log.debug("根据ID查找爬虫规则: {}", ruleId);
        
        if (ruleId == null || ruleId.trim().isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "规则ID不能为空");
        }
        
        try {
            return crawlerRuleRepository.findById(ruleId);
        } catch (Exception e) {
            log.error("根据ID查找爬虫规则失败: {}", ruleId, e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "查找规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据数据源标识符查找爬虫规则
     * 
     * @param sourceIdentifier 数据源标识符
     * @return 爬虫规则，如果不存在返回空
     */
    public Optional<CrawlerRule> findRuleBySourceIdentifier(String sourceIdentifier) {
        log.debug("根据数据源标识符查找爬虫规则: {}", sourceIdentifier);
        
        if (sourceIdentifier == null || sourceIdentifier.trim().isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "数据源标识符不能为空");
        }
        
        try {
            return crawlerRuleRepository.findBySourceIdentifier(sourceIdentifier);
        } catch (Exception e) {
            log.error("根据数据源标识符查找爬虫规则失败: {}", sourceIdentifier, e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "查找规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查找启用状态的爬虫规则
     * 
     * @return 启用的规则列表
     */
    public List<CrawlerRule> findEnabledRules() {
        log.debug("查找启用状态的爬虫规则");
        try {
            return crawlerRuleRepository.findByStatus(RuleStatus.ENABLED);
        } catch (Exception e) {
            log.error("查找启用状态的爬虫规则失败", e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "查找规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据数据源名称模糊查找爬虫规则
     * 
     * @param sourceName 数据源名称关键词
     * @return 匹配的规则列表
     */
    public List<CrawlerRule> findRulesBySourceNameLike(String sourceName) {
        log.debug("根据数据源名称模糊查找爬虫规则: {}", sourceName);
        
        if (sourceName == null || sourceName.trim().isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "数据源名称不能为空");
        }
        
        try {
            return crawlerRuleRepository.findBySourceNameLike(sourceName.trim());
        } catch (Exception e) {
            log.error("根据数据源名称模糊查找爬虫规则失败: {}", sourceName, e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "查找规则失败: " + e.getMessage(), e);
        }
    }

    // ==================== 规则创建和更新操作 ====================

    /**
     * 创建新的爬虫规则
     * 
     * @param rule 爬虫规则
     * @return 创建成功的规则
     */
    @Transactional
    public CrawlerRule createRule(CrawlerRule rule) {
        log.info("创建新的爬虫规则: {}", rule.getSourceIdentifier());
        
        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "爬虫规则不能为空");
        }
        
        try {
            // 验证规则配置
            ruleValidationService.validateRuleConfiguration(rule);
            
            // 检查数据源标识符是否已存在
            if (rule.getSourceIdentifier() != null) {
                Optional<CrawlerRule> existingRule = crawlerRuleRepository.findBySourceIdentifier(rule.getSourceIdentifier());
                if (existingRule.isPresent()) {
                    throw new CrawlerException(CrawlerErrorCode.RULE_CONFIG_ERROR, 
                        "数据源标识符已存在: " + rule.getSourceIdentifier());
                }
            }
            
            // 设置默认状态
            if (rule.getRuleStatus() == null) {
                rule.setRuleStatus(RuleStatus.TESTING);
            }
            
            // 保存规则
            CrawlerRule savedRule = crawlerRuleRepository.save(rule);
            log.info("爬虫规则创建成功: {} (ID: {})", savedRule.getSourceIdentifier(), savedRule.getId());
            
            return savedRule;
        } catch (CrawlerException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建爬虫规则失败: {}", rule.getSourceIdentifier(), e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "创建规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新爬虫规则
     * 
     * @param rule 爬虫规则
     * @return 更新成功的规则
     */
    @Transactional
    public CrawlerRule updateRule(CrawlerRule rule) {
        log.info("更新爬虫规则: {}", rule.getId());
        
        if (rule == null || rule.getId() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "规则ID不能为空");
        }
        
        try {
            // 检查规则是否存在
            Optional<CrawlerRule> existingRuleOpt = crawlerRuleRepository.findById(rule.getId());
            if (!existingRuleOpt.isPresent()) {
                throw new CrawlerException(CrawlerErrorCode.RULE_NOT_FOUND, "规则不存在: " + rule.getId());
            }
            
            CrawlerRule existingRule = existingRuleOpt.get();
            
            // 验证规则配置
            ruleValidationService.validateRuleConfiguration(rule);
            
            // 检查数据源标识符是否与其他规则冲突
            if (rule.getSourceIdentifier() != null && 
                !rule.getSourceIdentifier().equals(existingRule.getSourceIdentifier())) {
                Optional<CrawlerRule> conflictRule = crawlerRuleRepository.findBySourceIdentifier(rule.getSourceIdentifier());
                if (conflictRule.isPresent() && !conflictRule.get().getId().equals(rule.getId())) {
                    throw new CrawlerException(CrawlerErrorCode.RULE_CONFIG_ERROR, 
                        "数据源标识符已被其他规则使用: " + rule.getSourceIdentifier());
                }
            }
            
            // 更新版本号
            rule.updateVersion(generateNewVersion(existingRule.getRuleVersion()));
            
            // 保存更新
            CrawlerRule updatedRule = crawlerRuleRepository.save(rule);
            log.info("爬虫规则更新成功: {} (ID: {})", updatedRule.getSourceIdentifier(), updatedRule.getId());
            
            return updatedRule;
        } catch (CrawlerException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新爬虫规则失败: {}", rule.getId(), e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "更新规则失败: " + e.getMessage(), e);
        }
    }

    // ==================== 规则状态管理操作 ====================

    /**
     * 启用爬虫规则
     * 
     * @param ruleId 规则ID
     * @return 更新后的规则
     */
    @Transactional
    public CrawlerRule enableRule(String ruleId) {
        log.info("启用爬虫规则: {}", ruleId);
        return updateRuleStatus(ruleId, RuleStatus.ENABLED);
    }

    /**
     * 禁用爬虫规则
     * 
     * @param ruleId 规则ID
     * @return 更新后的规则
     */
    @Transactional
    public CrawlerRule disableRule(String ruleId) {
        log.info("禁用爬虫规则: {}", ruleId);
        return updateRuleStatus(ruleId, RuleStatus.DISABLED);
    }

    /**
     * 设置规则为维护状态
     * 
     * @param ruleId 规则ID
     * @return 更新后的规则
     */
    @Transactional
    public CrawlerRule setRuleMaintenance(String ruleId) {
        log.info("设置爬虫规则为维护状态: {}", ruleId);
        return updateRuleStatus(ruleId, RuleStatus.MAINTENANCE);
    }

    /**
     * 设置规则为测试状态
     * 
     * @param ruleId 规则ID
     * @return 更新后的规则
     */
    @Transactional
    public CrawlerRule setRuleTesting(String ruleId) {
        log.info("设置爬虫规则为测试状态: {}", ruleId);
        return updateRuleStatus(ruleId, RuleStatus.TESTING);
    }

    /**
     * 更新规则状态
     * 
     * @param ruleId 规则ID
     * @param newStatus 新状态
     * @return 更新后的规则
     */
    private CrawlerRule updateRuleStatus(String ruleId, RuleStatus newStatus) {
        if (ruleId == null || ruleId.trim().isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "规则ID不能为空");
        }
        
        try {
            Optional<CrawlerRule> ruleOpt = crawlerRuleRepository.findById(ruleId);
            if (!ruleOpt.isPresent()) {
                throw new CrawlerException(CrawlerErrorCode.RULE_NOT_FOUND, "规则不存在: " + ruleId);
            }
            
            CrawlerRule rule = ruleOpt.get();
            RuleStatus currentStatus = rule.getRuleStatus();
            
            // 检查状态转换是否合法
            if (currentStatus != null && !currentStatus.canTransitionTo(newStatus)) {
                throw new CrawlerException(CrawlerErrorCode.OPERATION_NOT_ALLOWED, 
                    String.format("不能从状态 %s 转换到状态 %s", currentStatus.getName(), newStatus.getName()));
            }
            
            // 如果是启用状态，需要验证规则配置
            if (newStatus == RuleStatus.ENABLED) {
                ruleValidationService.validateRuleConfiguration(rule);
            }
            
            rule.setRuleStatus(newStatus);
            CrawlerRule updatedRule = crawlerRuleRepository.save(rule);
            
            log.info("规则状态更新成功: {} -> {}", currentStatus, newStatus);
            return updatedRule;
        } catch (CrawlerException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新规则状态失败: {} -> {}", ruleId, newStatus, e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "更新规则状态失败: " + e.getMessage(), e);
        }
    }

    // ==================== 规则删除操作 ====================

    /**
     * 删除爬虫规则
     * 
     * @param ruleId 规则ID
     */
    @Transactional
    public void deleteRule(String ruleId) {
        log.info("删除爬虫规则: {}", ruleId);
        
        if (ruleId == null || ruleId.trim().isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "规则ID不能为空");
        }
        
        try {
            Optional<CrawlerRule> ruleOpt = crawlerRuleRepository.findById(ruleId);
            if (!ruleOpt.isPresent()) {
                throw new CrawlerException(CrawlerErrorCode.RULE_NOT_FOUND, "规则不存在: " + ruleId);
            }
            
            CrawlerRule rule = ruleOpt.get();
            
            // 检查规则是否可以删除
            if (rule.getRuleStatus() == RuleStatus.ENABLED) {
                throw new CrawlerException(CrawlerErrorCode.OPERATION_NOT_ALLOWED, 
                    "不能删除启用状态的规则，请先禁用规则");
            }
            
            crawlerRuleRepository.deleteById(ruleId);
            log.info("爬虫规则删除成功: {}", ruleId);
        } catch (CrawlerException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除爬虫规则失败: {}", ruleId, e);
            throw new CrawlerException(CrawlerErrorCode.DATABASE_ERROR, "删除规则失败: " + e.getMessage(), e);
        }
    }

    // ==================== 规则验证操作 ====================

    /**
     * 验证爬虫规则配置
     * 
     * @param rule 爬虫规则
     * @return 验证结果
     */
    public boolean validateRuleConfiguration(CrawlerRule rule) {
        log.debug("验证爬虫规则配置: {}", rule != null ? rule.getSourceIdentifier() : "null");
        
        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "爬虫规则不能为空");
        }
        
        try {
            return ruleValidationService.validateRuleConfiguration(rule);
        } catch (Exception e) {
            log.error("验证爬虫规则配置失败: {}", rule.getSourceIdentifier(), e);
            throw new CrawlerException(CrawlerErrorCode.RULE_VALIDATION_FAILED, "规则验证失败: " + e.getMessage(), e);
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 生成新的版本号
     * 
     * @param currentVersion 当前版本号
     * @return 新版本号
     */
    private String generateNewVersion(String currentVersion) {
        if (currentVersion == null || currentVersion.trim().isEmpty()) {
            return "1.0";
        }
        
        try {
            String[] parts = currentVersion.split("\\.");
            if (parts.length >= 2) {
                int major = Integer.parseInt(parts[0]);
                int minor = Integer.parseInt(parts[1]);
                return major + "." + (minor + 1);
            } else {
                return currentVersion + ".1";
            }
        } catch (NumberFormatException e) {
            return currentVersion + ".1";
        }
    }
}
