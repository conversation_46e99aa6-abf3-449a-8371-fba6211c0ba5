<template>
  <div class="global-tabs">
    <ul class="tabs-list">
      <li v-for="tab in allTabs" :key="tab.fullPath" :class="['tab-li', { active: tab.fullPath === activeTab }]">
        <div
          class="tab-label"
          @contextmenu.prevent="openContextMenu($event, tab)"
          @click="handleTabClick(tab)"
        >
          <span v-if="tab.fullPath === activeTab" class="tab-home-dot active"></span>
          <span class="tab-title">{{ tab.meta && tab.meta.title ? tab.meta.title : tab.name }}</span>
          <i
            v-if="tab.fullPath !== homePath"
            class="el-icon-close tab-close"
            @click.stop="handleTabRemove(tab.fullPath)"
          ></i>
        </div>
      </li>
    </ul>
    <!-- 右键菜单 -->
    <el-dropdown
      v-if="contextMenu.visible"
      :style="{ position: 'fixed', left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      trigger="manual"
      @command="handleMenuCommand"
      @visible-change="v => { if (!v) contextMenu.visible = false }"
      ref="dropdown"
    >
      <span></span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="refresh">刷新</el-dropdown-item>
        <el-dropdown-item command="close" :disabled="contextMenu.tab.fullPath === homePath">关闭当前</el-dropdown-item>
        <el-dropdown-item command="closeOthers">关闭其它</el-dropdown-item>
        <el-dropdown-item command="closeAll">关闭全部</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'GlobalTabs',
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    activeTab: {
      type: String,
      default: ''
    },
    homePath: {
      type: String,
      default: '/dashboard'
    }
  },
  data() {
    return {
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        tab: {}
      }
    }
  },
  computed: {
    allTabs() {
      const home = this.tabs.find(tab => tab.fullPath === this.homePath)
      const others = this.tabs.filter(tab => tab.fullPath !== this.homePath)
      return home ? [home, ...others] : others
    }
  },
  mounted() {
    document.addEventListener('click', this.handleGlobalClick)
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleGlobalClick)
  },
  methods: {
    handleGlobalClick() {
      if (this.contextMenu.visible) {
        this.contextMenu.visible = false
      }
    },
    handleTabClick(tab) {
      this.contextMenu.visible = false
      this.$emit('tab-change', tab.fullPath)
    },
    handleTabRemove(fullPath) {
      this.contextMenu.visible = false
      this.$emit('tab-remove', fullPath)
    },
    openContextMenu(e, tab) {
      this.contextMenu = { visible: true, x: e.clientX, y: e.clientY, tab }
      this.$nextTick(() => this.$refs.dropdown && this.$refs.dropdown.show())
    },
    handleMenuCommand(cmd) {
      const tab = this.contextMenu.tab
      this.contextMenu.visible = false
      if (cmd === 'refresh') this.$emit('tab-refresh', tab.fullPath)
      else if (cmd === 'close') this.handleTabRemove(tab.fullPath)
      else if (cmd === 'closeOthers') this.$emit('tab-close-others', tab.fullPath)
      else if (cmd === 'closeAll') this.$emit('tab-close-all')
    }
  }
}
</script>

<style scoped>
/* 标签栏整体容器 */
.global-tabs {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  min-height: 36px;
  padding: 0 8px;
  user-select: none;
  overflow-x: auto;
  position: relative;
  z-index: 10;
}

/* 标签列表容器 */
.tabs-list {
  display: flex;
  align-items: center;
  height: 38px;
  margin: 0;
  padding: 4px 8px 4px 10px; /* 上下留白，左右内边距 */
  list-style: none;
  background: #fff;
}

/* 单个标签样式 */
.tab-li {
  border-radius: 0;
  margin-left: -1px; /* 保证边框连续 */
  margin-right: 8px; /* 标签之间的左右间距 */
  position: relative;
  background: #fff;
  transition: background 0.18s, color 0.18s, border 0.18s, z-index 0.18s;
  border: 1px solid #e4e7ed;
  height: 32px;
  display: flex;
  align-items: center;
  color: #495060;
  min-width: 72px;
  max-width: 180px;
  z-index: 1;
  font-size: 13px;
  font-weight: 500;
}
/* 首页标签左侧不负margin，保证左边框完整 */
.tab-li:first-child {
  margin-left: 0;
}

/* 选中标签样式 */
.tab-li.active {
  color: #fff;
  background: #409EFF;
  border: 1px solid #409EFF;
  border-bottom: 1px solid #e4e7ed;
  z-index: 3;
  font-weight: bold;
}
/* 选中标签底部高亮条 */
.tab-li.active::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background: #409EFF;
  border-radius: 0 0 2px 2px;
  z-index: 4;
}

/* 未选中标签悬浮样式 */
.tab-li:not(.active):hover {
  background: #f4f8fd;
  color: #409EFF;
  border: 1px solid #409EFF;
  border-bottom: 1px solid #409EFF;
  z-index: 2;
  position: relative;
}

/* 标签内容区域 */
.tab-label {
  display: flex;
  align-items: center;
  height: 30px;
  font-size: 13px;
  color: inherit;
  padding: 0 14px;
  cursor: pointer;
  background: inherit;
  border-radius: 0;
  font-weight: 500;
  transition: background 0.18s, color 0.18s;
  max-width: 160px;
}

/* 标签标题文本溢出省略 */
.tab-title {
  white-space: nowrap;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

/* 关闭按钮样式 */
.tab-close {
  margin-left: 6px;
  font-size: 13px;
  color: #bbb;
  cursor: pointer;
  border-radius: 50%;
  padding: 0 2px;
  background: none;
  line-height: 1;
  transition: color 0.18s, background 0.18s, transform 0.18s;
}
.tab-li.active .tab-close { color: #fff; }
.tab-li:not(.active) .tab-close { color: #bbb; }
.tab-close:hover {
  color: #f56c6c;
  background: #fbeaea;
  transform: scale(1.18);
}

/* 首页小圆点样式 */
.tab-home-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #fff;
  margin-right: 3px;
  vertical-align: middle;
  box-shadow: 0 0 0 2px #409EFF;
}
.tab-li.active .tab-home-dot,
.tab-home-dot.active {
  background: #fff;
  box-shadow: 0 0 0 2px #409EFF;
}

/* 滚动条样式 */
::-webkit-scrollbar { height: 6px; background: #f4f6fa; }
::-webkit-scrollbar-thumb { background: #e0e3e7; border-radius: 4px; }
</style> 