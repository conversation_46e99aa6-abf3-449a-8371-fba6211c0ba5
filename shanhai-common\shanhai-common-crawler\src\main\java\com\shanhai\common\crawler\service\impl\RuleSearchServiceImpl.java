package com.shanhai.common.crawler.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.config.CrawlerRuleSearch;
import com.shanhai.common.crawler.repository.RuleSearchMapper;
import com.shanhai.common.crawler.service.RuleSearchService;
import org.springframework.stereotype.Service;

/**
 * 搜索页解析配置Service实现
 */
@Service
public class RuleSearchServiceImpl extends ServiceImpl<RuleSearchMapper, CrawlerRuleSearch> implements RuleSearchService {
} 