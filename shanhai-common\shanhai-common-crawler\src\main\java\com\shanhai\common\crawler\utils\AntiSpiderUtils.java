package com.shanhai.common.crawler.utils;

import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;

/**
 * 反爬虫工具类
 *
 * 提供User-Agent、代理、延迟等反爬虫策略的应用方法
 * 帮助爬虫程序规避网站的反爬虫检测
 *
 * <AUTHOR>
 */
@Slf4j
public class AntiSpiderUtils {

    /**
     * 应用反爬虫策略到Jsoup连接对象
     *
     * @param conn Jsoup连接对象
     * @param config 爬虫规则配置
     */
    public static void apply(Connection conn, CrawlerRuleNovel config) {
        if (config == null) {
            return;
        }

        // 设置User-Agent
        applyUserAgent(conn, config);

        // 设置代理（如果配置了的话）
        applyProxy(conn, config);
    }

    /**
     * 应用User-Agent设置
     */
    private static void applyUserAgent(Connection conn, CrawlerRuleNovel config) {
        if (config.getUserAgent() != null) {
            conn.userAgent(config.getUserAgent());
            log.debug("应用User-Agent: {}", config.getUserAgent());
        }
    }

    /**
     * 应用代理设置
     */
    private static void applyProxy(Connection conn, CrawlerRuleNovel config) {
        if (config.getCrawlerRuleAntiSpider() != null &&
            config.getCrawlerRuleAntiSpider().getProxyList() != null &&
            !config.getCrawlerRuleAntiSpider().getProxyList().isEmpty()) {
            log.debug("代理配置可用，但当前版本暂未实现代理功能");
            // TODO: 实现代理设置逻辑
        }
    }

    /**
     * 应用请求延迟
     *
     * @param config 爬虫规则配置
     */
    public static void applyDelay(CrawlerRuleNovel config) {
        if (config == null || config.getCrawlerRuleAntiSpider() == null) {
            return;
        }

        Integer minDelay = config.getCrawlerRuleAntiSpider().getMinDelayMs();
        Integer maxDelay = config.getCrawlerRuleAntiSpider().getMaxDelayMs();

        if (minDelay != null && maxDelay != null && minDelay > 0 && maxDelay > minDelay) {
            try {
                long delay = minDelay + (long) (Math.random() * (maxDelay - minDelay));
                log.debug("应用请求延迟: {}ms", delay);
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("请求延迟被中断");
            }
        }
    }
}