package com.shanhai.common.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jsoup.Connection;

@Getter
@AllArgsConstructor
public enum MethodEnum {
    GET(1, "GET", Connection.Method.GET),
    POST(2, "POST", Connection.Method.POST);

    @EnumValue // 标记数据库存的值是code
    private final int id;

    private final String code;

    private final Connection.Method method;

}
