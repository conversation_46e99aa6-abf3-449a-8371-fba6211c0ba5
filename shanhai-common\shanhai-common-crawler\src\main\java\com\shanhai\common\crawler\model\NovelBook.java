package com.shanhai.common.crawler.model;

import lombok.Data;
import java.util.List;

/**
 * 小说书籍实体
 * 包含书籍基本信息和章节列表。
 */
@Data
public class NovelBook {
    /** 书名 */
    private String name;
    /** 书名（冗余，兼容 title 字段） */
    private String title;
    /** 作者 */
    private String author;
    /** 封面图片URL */
    private String coverUrl;
    /** 简介 */
    private String intro;
    /** 分类 */
    private String category;
    /** 字数 */
    private String wordCount;
    /** 最新章节 */
    private String lastChapter;
    /** 状态（连载/完结） */
    private String status;
    /** 更新时间 */
    private String updateTime;
    /** 详情页URL */
    private String bookUrl;
    /** 章节目录页URL */
    private String chapterListUrl;
    /** 章节列表 */
    private List<NovelChapter> chapters;

    /**
     * 小说章节实体
     */
    @Data
    public static class NovelChapter {
        /** 章节标题 */
        private String title;
        /** 章节URL */
        private String url;
        /** 章节内容 */
        private String content;
    }
} 