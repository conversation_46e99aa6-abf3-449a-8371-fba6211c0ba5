<template>
  <div class="role-page">
    <el-card>
      <div slot="header">
        <span>角色管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">
          新增角色
        </el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="角色名称">
          <el-input v-model="searchForm.roleName" placeholder="请输入角色名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格 -->
      <el-table :data="roleList" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="roleName" label="角色名称" />
        <el-table-column prop="roleCode" label="角色编码" />
        <el-table-column prop="description" label="角色描述" />
        <el-table-column prop="userCount" label="用户数量" width="100" />
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="primary" @click="handlePermission(scope.row)">权限</el-button>
            <el-button 
              size="mini" 
              :type="scope.row.status === '1' ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === '1' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;"
      />
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'SystemRole',
  data() {
    return {
      searchForm: {
        roleName: '',
        status: ''
      },
      roleList: [
        {
          id: 1,
          roleName: '超级管理员',
          roleCode: 'SUPER_ADMIN',
          description: '系统超级管理员，拥有所有权限',
          userCount: 1,
          status: '1',
          createTime: '2024-01-15 10:30'
        },
        {
          id: 2,
          roleName: '管理员',
          roleCode: 'ADMIN',
          description: '系统管理员，拥有大部分权限',
          userCount: 2,
          status: '1',
          createTime: '2024-01-14 15:20'
        },
        {
          id: 3,
          roleName: '普通用户',
          roleCode: 'USER',
          description: '普通用户，拥有基本权限',
          userCount: 5,
          status: '1',
          createTime: '2024-01-13 09:45'
        },
        {
          id: 4,
          roleName: '访客',
          roleCode: 'GUEST',
          description: '访客用户，只有查看权限',
          userCount: 0,
          status: '0',
          createTime: '2024-01-12 14:30'
        }
      ],
      pagination: {
        current: 1,
        size: 10,
        total: 4
      }
    }
  },
  methods: {
    handleAdd() {
      this.$message.info('新增角色功能待实现')
    },
    handleEdit(row) {
      this.$message.info(`编辑角色：${row.roleName}`)
    },
    handlePermission(row) {
      this.$message.info(`配置角色权限：${row.roleName}`)
    },
    handleToggleStatus(row) {
      const newStatus = row.status === '1' ? '0' : '1'
      const statusText = newStatus === '1' ? '启用' : '禁用'
      this.$confirm(`确定要${statusText}角色"${row.roleName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = newStatus
        this.$message.success(`${statusText}成功`)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除角色"${row.roleName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSearch() {
      this.$message.info('搜索功能待实现')
    },
    handleReset() {
      this.searchForm = {
        roleName: '',
        status: ''
      }
      this.$message.info('重置成功')
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.$message.info(`每页显示 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.$message.info(`当前页：${val}`)
    }
  }
}
</script>

<style scoped>
.role-page {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style> 