<template>
  <div class="user-page">
    <div class="user-table-wrapper">
      <!-- 查询表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form" @submit.native.prevent="handleSearch">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable @keyup.enter.native="handleSearch" size="mini" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable size="mini">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!searchCollapse" label="邮箱">
          <el-input v-model="searchForm.email" placeholder="请输入邮箱" clearable size="mini" />
        </el-form-item>
        <el-form-item v-if="!searchCollapse" label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable size="mini" />
        </el-form-item>
        <el-form-item class="search-btns">
          <el-button type="primary" size="mini" @click="handleSearch">搜索</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
          <el-button type="text" size="mini" @click="searchCollapse = !searchCollapse">
            {{ searchCollapse ? '展开' : '收起' }}
            <i :class="searchCollapse ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </el-button>
        </el-form-item>
      </el-form>
      <!-- 操作栏 -->
      <div class="table-action-bar">
        <div class="action-buttons">
          <el-button size="mini" type="primary" icon="el-icon-view" @click="handleView">查看</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
          <el-button size="mini" type="warning" icon="el-icon-edit" @click="handleEditSelected" :disabled="multipleSelection.length !== 1">修改</el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDeleteSelected" :disabled="multipleSelection.length === 0">删除</el-button>
        </div>
        <!-- 列设置 -->
        <el-tooltip content="自定义列显示/顺序" placement="top">
          <el-dropdown ref="dropdown">
            <el-button type="primary" icon="el-icon-setting" circle></el-button>
            <el-dropdown-menu slot="dropdown" class="column-dropdown-menu">
              <div class="column-dropdown-actions">
                <el-button size="mini" @click.stop="showAllColumns" type="success" plain>全部显示</el-button>
                <el-button size="mini" @click.stop="hideAllColumns" type="warning" plain>全部隐藏</el-button>
              </div>
              <draggable v-model="localColumns" handle=".drag-handle" :animation="200" @end="updateColumns">
                <div v-for="col in localColumns" :key="col.prop" class="column-dropdown-item">
                  <i class="el-icon-rank drag-handle" style="cursor:move;margin-right:6px;"></i>
                  <el-checkbox v-model="col.visible">{{ col.label }}</el-checkbox>
                </div>
              </draggable>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
      </div>
      <!-- 表格 -->
      <el-table :data="userList" style="width: 100%" border stripe class="user-table"
        :loading="tableLoading"
        row-class-name="custom-row"
        empty-text="暂无数据，请点击新增用户"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" fixed="left" align="center" />
        <el-table-column
          v-for="col in localColumns"
          v-if="col.visible"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :width="col.width"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div v-if="col.prop === 'status'">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'"
                :style="scope.row.status === '1' ? 'background: var(--success); color: #fff; border: none;' : 'background: var(--danger); color: #fff; border: none;'">
                {{ scope.row.status === '1' ? '启用' : '禁用' }}
              </el-tag>
            </div>
            <span v-else>{{ scope.row[col.prop] }}</span>
          </template>
        </el-table-column>
        <!-- 操作列始终显示，单独处理 -->
        <el-table-column
          label="操作"
          width="220"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleToggleStatus(scope.row)">
              {{ scope.row.status === '1' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="table-footer-bar">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" class="user-pagination" />
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  name: 'SystemUser',
  components: { draggable },
  data() {
    return {
      searchForm: {
        username: '',
        status: ''
      },
      searchCollapse: true,
      userList: [
        {
          id: 1,
          username: 'admin',
          nickname: '管理员',
          email: '<EMAIL>',
          phone: '13800138000',
          role: '超级管理员',
          status: '1',
          createTime: '2024-01-15 10:30'
        },
        {
          id: 2,
          username: 'user1',
          nickname: '普通用户',
          email: '<EMAIL>',
          phone: '13800138001',
          role: '普通用户',
          status: '1',
          createTime: '2024-01-14 15:20'
        },
        {
          id: 3,
          username: 'user2',
          nickname: '测试用户',
          email: '<EMAIL>',
          phone: '13800138002',
          role: '普通用户',
          status: '0',
          createTime: '2024-01-13 09:45'
        }
      ],
      pagination: {
        current: 1,
        size: 10,
        total: 3
      },
      multipleSelection: [],
      columns: [
        { label: 'ID', prop: 'id', visible: true, width: 80 },
        { label: '用户名', prop: 'username', visible: true, width: 120 },
        { label: '昵称', prop: 'nickname', visible: true, width: 100 },
        { label: '邮箱', prop: 'email', visible: true, width: 180 },
        { label: '手机号', prop: 'phone', visible: true, width: 130 },
        { label: '角色', prop: 'role', visible: true, width: 120 },
        { label: '状态', prop: 'status', visible: true, width: 80 },
        { label: '创建时间', prop: 'createTime', visible: true, width: 160 }
      ],
      localColumns: [],
      tableLoading: false
    }
  },
  watch: {
    columns: {
      handler(val) {
        localStorage.setItem('user_columns', JSON.stringify(val));
        this.localColumns = JSON.parse(JSON.stringify(val));
      },
      deep: true
    },
    localColumns: {
      handler(val) {
        this.columns = JSON.parse(JSON.stringify(val));
      },
      deep: true
    }
  },
  mounted() {
    // 列设置持久化
    const saved = localStorage.getItem('user_columns');
    if (saved) {
      this.columns = JSON.parse(saved);
    }
    this.localColumns = JSON.parse(JSON.stringify(this.columns));
  },
  methods: {
    handleView() {
      // 查看逻辑
    },
    handleAdd() {
      this.$message.info('新增用户功能待实现')
    },
    handleEditSelected() {
      if (this.multipleSelection.length !== 1) return;
      this.handleEdit(this.multipleSelection[0]);
    },
    handleDeleteSelected() {
      if (this.multipleSelection.length === 0) return;
      this.$confirm(`确定要删除选中的${this.multipleSelection.length}个用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {});
    },
    handleEdit(row) {
      this.$message.info(`编辑用户：${row.username}`)
    },
    handleDelete(row) {
      this.$confirm(`确定要删除用户"${row.username}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {});
    },
    handleToggleStatus(row) {
      const newStatus = row.status === '1' ? '0' : '1'
      const statusText = newStatus === '1' ? '启用' : '禁用'
      this.$confirm(`确定要${statusText}用户"${row.username}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = newStatus
        this.$message.success(`${statusText}成功`)
      }).catch(() => {})
    },
    handleSearch() {
      this.pagination.current = 1
      this.$message.info('搜索功能待实现')
    },
    handleReset() {
      this.searchForm = {
        username: '',
        status: ''
      }
      this.pagination.current = 1
      this.$message.info('重置成功')
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.$message.info(`每页显示 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.$message.info(`当前页：${val}`)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    showAllColumns() {
      this.localColumns.forEach(col => col.visible = true);
    },
    hideAllColumns() {
      this.localColumns.forEach(col => { if (col.prop !== 'action') col.visible = false; });
    },
    updateColumns() {
      this.columns = JSON.parse(JSON.stringify(this.localColumns));
    }
  }
}
</script>

<style scoped>
.user-page {
  padding: 0;
  background: #f6f7fb;
}
.user-header {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 16px;
  gap: 12px;
}
.search-form {
  background: #fff;
  border-radius: 8px;
  padding: 10px 16px 6px 16px;
  margin-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 8px;
  box-shadow: none;
}
.add-btn-item {
  margin-right: 8px;
}
.search-btns {
  margin-left: 8px;
}
.user-table-wrapper {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  padding: 0 0 8px 0;
  overflow-x: auto;
  margin-bottom: 16px;
}
.table-action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 12px 0 8px 0;
}
.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}
.table-footer-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 0 8px 4px 0;
  background: #fff;
  border-radius: 0 0 10px 10px;
}
.user-table {
  border-radius: 10px;
  font-size: 14px;
  min-width: 900px;
}
.user-table ::v-deep .el-table__body tr {
  height: 48px;
}
.user-table ::v-deep .el-table__body tr:hover > td {
  background: #f7fafd !important;
}
.user-table ::v-deep .el-table__row--striped > td {
  background: #fafbfc !important;
}
.user-table ::v-deep .el-tag {
  border-radius: 4px;
  font-size: 13px;
}
.user-table ::v-deep .el-table th {
  background: #f4f6fa;
  color: #333;
  font-weight: 500;
}
.user-table ::v-deep .el-button {
  margin-right: 6px;
  padding: 0 8px;
}
.user-table ::v-deep .el-button:last-child {
  margin-right: 0;
}
.user-pagination {
  margin-top: 12px;
  text-align: right;
  background: #fff;
  padding: 8px 0 0 0;
  border-radius: 0 0 10px 10px;
}
.column-dropdown-menu {
  padding: 10px 12px 6px 12px;
  min-width: 150px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.08);
  border: none;
}
.column-dropdown-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  gap: 6px;
}
.column-dropdown-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  padding: 2px 0 2px 2px;
  border-radius: 5px;
  transition: background 0.2s;
}
.column-dropdown-item:hover {
  background: #f5f7fa;
}
.drag-handle {
  color: #bbb;
  font-size: 15px;
  cursor: move;
}
/* 控件mini尺寸统一 */
.el-button, .el-input, .el-select, .el-pagination, .el-checkbox {
  font-size: 13px !important;
  height: 28px !important;
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>