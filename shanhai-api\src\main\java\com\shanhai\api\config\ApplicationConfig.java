package com.shanhai.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.util.Arrays;

/**
 * 应用程序主配置类
 * <p>
 * 统一管理应用程序的核心配置，包括：
 * <ul>
 *   <li>跨域配置 (CORS)</li>
 *   <li>Web MVC配置</li>
 *   <li>应用信息配置</li>
 *   <li>其他通用配置</li>
 * </ul>
 *
 * <p>设计原则：
 * <ul>
 *   <li>配置集中管理，避免分散</li>
 *   <li>环境配置分离，支持多环境</li>
 *   <li>配置项验证，确保有效性</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024-01-01
 */
@Slf4j
@Configuration
@Import({
    // 导入其他配置类
    // DatabaseConfig.class,
    // SecurityConfig.class,
    // CacheConfig.class
})
public class ApplicationConfig implements WebMvcConfigurer {

    @Value("${spring.application.name:shanhai-novel-crawler}")
    private String applicationName;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${application.version:2.0.0}")
    private String applicationVersion;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * 跨域配置
     * 配置允许的跨域请求，支持前后端分离开发
     *
     * @return CORS配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // 允许的源地址
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:*",
            "http://127.0.0.1:*",
            "https://localhost:*",
            "https://127.0.0.1:*"
        ));

        // 允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"
        ));

        // 允许的请求头
        configuration.setAllowedHeaders(Arrays.asList("*"));

        // 允许携带认证信息
        configuration.setAllowCredentials(true);

        // 预检请求的缓存时间（秒）
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }

    /**
     * 应用启动后的初始化操作
     * 打印应用基本信息和配置状态
     */
    @PostConstruct
    public void init() {
        log.info("========================================");
        log.info("=== 山海小说爬虫系统启动完成 ===");
        log.info("应用名称: {}", applicationName);
        log.info("系统版本: {}", applicationVersion);
        log.info("运行环境: {}", activeProfile);
        log.info("服务端口: {}", serverPort);
        log.info("启动时间: {}", java.time.LocalDateTime.now());
        log.info("========================================");

        // 根据环境打印不同的提示信息
        if ("dev".equals(activeProfile)) {
            log.info("开发环境已启动，前端访问地址: http://localhost:{}", serverPort);
            log.info("API文档地址: http://localhost:{}/swagger-ui.html", serverPort);
        } else if ("prod".equals(activeProfile)) {
            log.info("生产环境已启动，请确保安全配置已正确设置");
        }
    }
}
