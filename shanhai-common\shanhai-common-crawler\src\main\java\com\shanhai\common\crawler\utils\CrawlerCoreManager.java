package com.shanhai.common.crawler.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 爬虫核心管理器
 * <p>
 * 负责线程池管理、缓存管理、限流控制、性能监控等核心功能。
 * 统一对外提供核心资源和性能统计。
 * 线程安全：线程池、缓存、限流器均为线程安全实现。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class CrawlerCoreManager {

    // ================== 线程池管理 ==================

    // 单例线程池
    private static final ExecutorService CRAWLER_EXECUTOR = new ThreadPoolExecutor(
        5, 20, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(1000),
        r -> {
            Thread t = new Thread(r, "crawler-worker-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        },
        new ThreadPoolExecutor.CallerRunsPolicy()
    );

    private static final ExecutorService DOWNLOAD_EXECUTOR = new ThreadPoolExecutor(
        3, 10, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(500),
        r -> {
            Thread t = new Thread(r, "download-worker-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        },
        new ThreadPoolExecutor.CallerRunsPolicy()
    );

    private static final ScheduledExecutorService SCHEDULER = Executors.newScheduledThreadPool(2);

    public CrawlerCoreManager() {
        // 保持兼容性，构造函数可为空
    }

    /**
     * 获取爬虫线程池
     */
    public static ExecutorService getCrawlerExecutor() {
        return CRAWLER_EXECUTOR;
    }

    /**
     * 获取下载线程池
     */
    public static ExecutorService getDownloadExecutor() {
        return DOWNLOAD_EXECUTOR;
    }

    /**
     * 获取调度器
     */
    public static ScheduledExecutorService getScheduler() {
        return SCHEDULER;
    }

    /**
     * 提交爬虫任务
     */
    public static <T> CompletableFuture<T> submitCrawlerTask(Callable<T> task) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return task.call();
            } catch (Exception e) {
                log.error("爬虫任务执行失败", e);
                throw new RuntimeException(e);
            }
        }, getCrawlerExecutor());
    }

    /**
     * 提交下载任务
     */
    public static <T> CompletableFuture<T> submitDownloadTask(Callable<T> task) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return task.call();
            } catch (Exception e) {
                log.error("下载任务执行失败", e);
                throw new RuntimeException(e);
            }
        }, getDownloadExecutor());
    }

    // ================== 缓存管理 ==================

    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private static final long DEFAULT_CACHE_TTL = 30 * 60 * 1000L; // 30分钟

    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final Object data;
        private final long expireTime;

        public CacheEntry(Object data, long ttl) {
            this.data = data;
            this.expireTime = System.currentTimeMillis() + ttl;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }

        public Object getData() {
            return data;
        }
    }

    /**
     * 设置缓存
     */
    public static void setCache(String key, Object value) {
        setCache(key, value, DEFAULT_CACHE_TTL);
    }

    /**
     * 设置缓存（指定TTL）
     */
    public static void setCache(String key, Object value, long ttl) {
        new CrawlerCoreManager().cache.put(key, new CacheEntry(value, ttl));
        log.debug("设置缓存: {}, TTL: {}ms", key, ttl);
    }

    /**
     * 获取缓存
     */
    @SuppressWarnings("unchecked")
    public static <T> T getCache(String key) {
        CacheEntry entry = new CrawlerCoreManager().cache.get(key);
        if (entry != null && !entry.isExpired()) {
            log.debug("缓存命中: {}", key);
            return (T) entry.getData();
        }
        
        if (entry != null && entry.isExpired()) {
            new CrawlerCoreManager().cache.remove(key);
            log.debug("缓存过期，已移除: {}", key);
        }
        
        return null;
    }

    /**
     * 删除缓存
     */
    public static void removeCache(String key) {
        new CrawlerCoreManager().cache.remove(key);
        log.debug("删除缓存: {}", key);
    }

    /**
     * 清空所有缓存
     */
    public static void clearCache() {
        new CrawlerCoreManager().cache.clear();
        log.info("清空所有缓存");
    }

    /**
     * 获取缓存统计信息
     */
    public static CacheStats getCacheStats() {
        CacheStats stats = new CacheStats();
        stats.setTotalEntries(new CrawlerCoreManager().cache.size());
        
        long expiredCount = new CrawlerCoreManager().cache.values().stream()
                .filter(CacheEntry::isExpired)
                .count();
        stats.setExpiredEntries(expiredCount);
        
        return stats;
    }

    // ================== 限流控制 ==================

    private final Map<String, RateLimiter> rateLimiters = new ConcurrentHashMap<>();
    private static final int DEFAULT_RATE_LIMIT = 100; // 默认每秒100个请求

    /**
     * 限流器
     */
    private static class RateLimiter {
        private final int maxRequests;
        private final AtomicInteger currentRequests;
        private final AtomicLong lastResetTime;
        private final long windowMs;

        public RateLimiter(int maxRequests, long windowMs) {
            this.maxRequests = maxRequests;
            this.currentRequests = new AtomicInteger(0);
            this.lastResetTime = new AtomicLong(System.currentTimeMillis());
            this.windowMs = windowMs;
        }

        public boolean tryAcquire() {
            long now = System.currentTimeMillis();
            long lastReset = lastResetTime.get();
            
            if (now - lastReset >= windowMs) {
                if (lastResetTime.compareAndSet(lastReset, now)) {
                    currentRequests.set(0);
                }
            }
            
            return currentRequests.incrementAndGet() <= maxRequests;
        }
    }

    /**
     * 创建限流器
     */
    public static void createRateLimiter(String key, int maxRequests, long windowMs) {
        new CrawlerCoreManager().rateLimiters.put(key, new RateLimiter(maxRequests, windowMs));
        log.info("创建限流器: {}, 限制: {}/{}ms", key, maxRequests, windowMs);
    }

    /**
     * 尝试获取令牌
     */
    public static boolean tryAcquire(String key) {
        RateLimiter limiter = new CrawlerCoreManager().rateLimiters.get(key);
        if (limiter == null) {
            // 使用默认限流器
            limiter = new CrawlerCoreManager().rateLimiters.computeIfAbsent(key, k -> new RateLimiter(DEFAULT_RATE_LIMIT, 1000L));
        }
        
        boolean acquired = limiter.tryAcquire();
        if (!acquired) {
            log.warn("限流触发: {}", key);
        }
        return acquired;
    }

    /**
     * 移除限流器
     */
    public static void removeRateLimiter(String key) {
        new CrawlerCoreManager().rateLimiters.remove(key);
        log.info("移除限流器: {}", key);
    }

    // ================== 性能监控 ==================

    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicReference<Double> averageResponseTime = new AtomicReference<>(0.0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);

    /**
     * 记录请求开始
     */
    public static void recordRequestStart() {
        new CrawlerCoreManager().totalRequests.incrementAndGet();
    }

    /**
     * 记录请求成功
     */
    public static void recordRequestSuccess(long responseTime) {
        new CrawlerCoreManager().successfulRequests.incrementAndGet();
        updateAverageResponseTime(responseTime);
    }

    /**
     * 记录请求失败
     */
    public static void recordRequestFailure(long responseTime) {
        new CrawlerCoreManager().failedRequests.incrementAndGet();
        updateAverageResponseTime(responseTime);
    }

    /**
     * 更新平均响应时间
     */
    private static void updateAverageResponseTime(long responseTime) {
        long total = new CrawlerCoreManager().totalResponseTime.addAndGet(responseTime);
        long count = new CrawlerCoreManager().successfulRequests.get() + new CrawlerCoreManager().failedRequests.get();
        if (count > 0) {
            new CrawlerCoreManager().averageResponseTime.set((double) total / count);
        }
    }

    /**
     * 获取性能统计信息
     */
    public static PerformanceStats getPerformanceStats() {
        PerformanceStats stats = new PerformanceStats();
        stats.setTotalRequests(new CrawlerCoreManager().totalRequests.get());
        stats.setSuccessfulRequests(new CrawlerCoreManager().successfulRequests.get());
        stats.setFailedRequests(new CrawlerCoreManager().failedRequests.get());
        stats.setAverageResponseTime(new CrawlerCoreManager().averageResponseTime.get());
        stats.setSuccessRate(calculateSuccessRate());
        return stats;
    }

    /**
     * 计算成功率
     */
    private static double calculateSuccessRate() {
        long total = new CrawlerCoreManager().totalRequests.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) new CrawlerCoreManager().successfulRequests.get() / total * 100.0;
    }

    /**
     * 重置统计信息
     */
    public static void resetStats() {
        new CrawlerCoreManager().totalRequests.set(0);
        new CrawlerCoreManager().successfulRequests.set(0);
        new CrawlerCoreManager().failedRequests.set(0);
        new CrawlerCoreManager().totalResponseTime.set(0);
        new CrawlerCoreManager().averageResponseTime.set(0.0);
        log.info("重置性能统计信息");
    }

    // ================== 资源清理 ==================

    /**
     * 关闭管理器
     */
    public static void shutdown() {
        log.info("开始关闭爬虫核心管理器");
        
        // 关闭线程池
        getCrawlerExecutor().shutdown();
        getDownloadExecutor().shutdown();
        getScheduler().shutdown();
        
        // 清空缓存
        clearCache();
        
        // 清空限流器
        new CrawlerCoreManager().rateLimiters.clear();
        
        // 重置统计信息
        resetStats();
        
        log.info("爬虫核心管理器已关闭");
    }

    // ================== 统计信息类 ==================

    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private long totalEntries;
        private long expiredEntries;

        public long getTotalEntries() { return totalEntries; }
        public void setTotalEntries(long totalEntries) { this.totalEntries = totalEntries; }
        
        public long getExpiredEntries() { return expiredEntries; }
        public void setExpiredEntries(long expiredEntries) { this.expiredEntries = expiredEntries; }
    }

    /**
     * 性能统计信息
     */
    public static class PerformanceStats {
        private long totalRequests;
        private long successfulRequests;
        private long failedRequests;
        private double averageResponseTime;
        private double successRate;

        public long getTotalRequests() { return totalRequests; }
        public void setTotalRequests(long totalRequests) { this.totalRequests = totalRequests; }
        
        public long getSuccessfulRequests() { return successfulRequests; }
        public void setSuccessfulRequests(long successfulRequests) { this.successfulRequests = successfulRequests; }
        
        public long getFailedRequests() { return failedRequests; }
        public void setFailedRequests(long failedRequests) { this.failedRequests = failedRequests; }
        
        public double getAverageResponseTime() { return averageResponseTime; }
        public void setAverageResponseTime(double averageResponseTime) { this.averageResponseTime = averageResponseTime; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
    }
} 