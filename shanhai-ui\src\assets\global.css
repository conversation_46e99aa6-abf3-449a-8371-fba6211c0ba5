/* 全局字体与基础色彩 */
body, html, #app {
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: #f4f6fa;
  color: #222;
  font-size: 15px;
  line-height: 1.6;
  height: 100vh;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

:root {
  --primary: rgb(64,158,255);
  --primary-hover: #66b1ff;
  --success: #67c23a;
  --warning: #e6a23c;
  --danger: #f56c6c;
  --sidebar-bg: linear-gradient(180deg, #2d3a4b 0%, #304156 100%);
  --sidebar-active: #409EFF;
  --sidebar-hover: #263445;
  --header-bg: #fff;
  --card-bg: #fff;
  --card-radius: 10px;
  --card-shadow: 0 2px 12px #0000000d;
  --border: #e4e7ed;
  --text-main: #222;
  --text-sub: #666;
  --disabled: #e0e3e7;
}

/* 按钮美化 */
.el-button, .el-button--primary, .el-button--success, .el-button--danger, .el-button--warning {
  border-radius: 4px;
  font-weight: 500;
  transition: background 0.2s, border 0.2s, color 0.2s, box-shadow 0.2s;
}
.el-button--primary {
  background: var(--primary);
  border-color: var(--primary);
}
.el-button--primary:hover, .el-button--primary:focus {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}
.el-button--success {
  background: var(--success);
  border-color: var(--success);
}
.el-button--danger {
  background: var(--danger);
  border-color: var(--danger);
}
.el-button--warning {
  background: var(--warning);
  border-color: var(--warning);
}
.el-button[disabled], .el-button.is-disabled {
  background: var(--disabled) !important;
  color: #aaa !important;
  border-color: var(--disabled) !important;
  cursor: not-allowed;
}

/* 卡片化内容区 */
.main-content {
  padding: 32px 32px 16px 32px;
  background: #f4f6fa;
  min-height: 0;
  box-sizing: border-box;
  overflow-x: auto;
}
.el-card, .main-card {
  background: var(--card-bg);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  border: none;
  margin-bottom: 24px;
  transition: box-shadow 0.2s;
  max-width: 100%;
}
.el-card:hover, .main-card:hover {
  box-shadow: 0 4px 24px #b3e5fc33;
}

/* 表格美化 */
.el-table {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  font-size: 14px;
  max-width: 100%;
}
.el-table th, .el-table td {
  background: #fff;
  color: #333;
  padding: 10px 8px;
}
.el-table th {
  background: #f8fafb;
  font-weight: 600;
  color: #222;
}
.el-table__row:hover td {
  background: #f0f7ff;
}
.el-table .el-button {
  font-size: 13px;
  padding: 2px 10px;
}

/* 表单美化 */
.el-form-item__label {
  color: #666;
  font-weight: 500;
  font-size: 14px;
}
.el-input, .el-select, .el-textarea {
  border-radius: 4px;
  font-size: 14px;
}
.el-input:focus, .el-select:focus, .el-textarea:focus {
  border-color: var(--primary);
}

/* 弹窗美化 */
.el-dialog {
  border-radius: 8px;
  box-shadow: 0 8px 32px #00000022;
}
.el-dialog__header {
  font-size: 17px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
}
.el-dialog__footer {
  text-align: right;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  background: #f4f6fa;
}
::-webkit-scrollbar-thumb {
  background: #e0e3e7;
  border-radius: 4px;
}

/* 统一分割线 */
hr {
  border: none;
  border-top: 1px solid #eaeaea;
  margin: 24px 0;
}

/* 动画 */
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.4s cubic-bezier(.55,0,.1,1);
}
.fade-slide-enter, .fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
} 