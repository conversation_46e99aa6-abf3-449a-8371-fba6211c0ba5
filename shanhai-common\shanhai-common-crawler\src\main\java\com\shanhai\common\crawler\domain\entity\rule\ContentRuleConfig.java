package com.shanhai.common.crawler.domain.entity.rule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 内容规则配置实体
 * <p>
 * 定义如何从章节内容页提取正文内容的规则配置。
 * 包括内容提取、清洗、分页处理等功能。
 * 
 * <p>主要功能：
 * <ul>
 *   <li>配置章节内容的提取规则</li>
 *   <li>支持分页内容的自动合并</li>
 *   <li>提供内容清洗和格式化功能</li>
 *   <li>支持动态加载的内容页面</li>
 * </ul>
 * 
 * <p>支持的内容处理：
 * <ul>
 *   <li>基本提取：章节标题、正文内容</li>
 *   <li>分页处理：自动翻页、内容合并</li>
 *   <li>内容清洗：移除广告、格式化文本</li>
 *   <li>质量控制：内容验证、长度检查</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_content", comment = "内容规则配置表")
@TableName("crawler_rule_content")
public class ContentRuleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 关联的爬虫规则ID
     */
    @Column(value = "rule_id", comment = "关联的爬虫规则ID", length = 32)
    private String ruleId;

    // ==================== 基本内容提取配置 ====================

    /**
     * 章节标题选择器
     * 用于提取章节标题的CSS选择器或JSONPath
     */
    @Column(value = "chapter_title_selector", comment = "章节标题选择器", length = 200)
    private String chapterTitleSelector;

    /**
     * 正文内容选择器
     * 用于提取章节正文内容的选择器
     */
    @NotBlank(message = "正文内容选择器不能为空")
    @Column(value = "content_selector", comment = "正文内容选择器", length = 200)
    private String contentSelector;

    /**
     * 内容容器选择器
     * 用于选择包含正文内容的容器元素
     */
    @Column(value = "content_container_selector", comment = "内容容器选择器", length = 200)
    private String contentContainerSelector;

    /**
     * 作者名称选择器
     * 用于提取作者名称的选择器
     */
    @Column(value = "author_name_selector", comment = "作者名称选择器", length = 200)
    private String authorNameSelector;

    /**
     * 发布时间选择器
     * 用于提取章节发布时间的选择器
     */
    @Column(value = "publish_time_selector", comment = "发布时间选择器", length = 200)
    private String publishTimeSelector;

    // ==================== 内容清洗配置 ====================

    /**
     * 需要移除的元素选择器列表
     * 用于移除广告、导航等不需要的元素
     */
    @Column(value = "remove_selectors", comment = "需要移除的元素选择器(JSON数组)", type = "TEXT")
    private List<String> removeSelectors;

    /**
     * 内容过滤正则表达式
     * 用于过滤和清理内容的正则表达式
     */
    @Column(value = "content_filter_regex", comment = "内容过滤正则表达式", length = 1000)
    private String contentFilterRegex;

    /**
     * 替换规则
     * 用于内容替换的规则配置（正则表达式 -> 替换文本）
     */
    @Column(value = "replacement_rules", comment = "替换规则(JSON格式)", type = "TEXT")
    private Map<String, String> replacementRules;

    /**
     * 是否保留HTML格式
     * 在提取内容时是否保留HTML标签
     */
    @Builder.Default
    @Column(value = "keep_html_format", comment = "是否保留HTML格式")
    private Boolean keepHtmlFormat = false;

    /**
     * 内容分隔符
     * 多段内容之间的分隔符
     */
    @Builder.Default
    @Column(value = "content_separator", comment = "内容分隔符", length = 10)
    private String contentSeparator = "\n";

    /**
     * 段落分隔符
     * 段落之间的分隔符
     */
    @Builder.Default
    @Column(value = "paragraph_separator", comment = "段落分隔符", length = 10)
    private String paragraphSeparator = "\n\n";

    // ==================== 分页处理配置 ====================

    /**
     * 是否支持分页
     * 章节内容是否分页显示
     */
    @Builder.Default
    @Column(value = "pagination_enabled", comment = "是否支持分页")
    private Boolean paginationEnabled = false;

    /**
     * 下一页选择器
     * 用于获取下一页链接的选择器
     */
    @Column(value = "next_page_selector", comment = "下一页选择器", length = 200)
    private String nextPageSelector;

    /**
     * 下一页链接属性名
     * 提取下一页链接时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "next_page_attribute", comment = "下一页链接属性名", length = 20)
    private String nextPageAttribute = "href";

    /**
     * 最大页数限制
     * 章节内容的最大页数限制
     */
    @Builder.Default
    @Min(value = 1, message = "最大页数不能小于1")
    @Max(value = 50, message = "最大页数不能大于50")
    @Column(value = "max_page_limit", comment = "最大页数限制")
    private Integer maxPageLimit = 10;

    /**
     * 页面间隔时间（毫秒）
     * 访问分页时的间隔时间
     */
    @Builder.Default
    @Column(value = "page_interval_millis", comment = "页面间隔时间(毫秒)")
    private Integer pageIntervalMillis = 1000;

    /**
     * 分页内容合并方式
     * 分页内容的合并方式（APPEND/REPLACE/SMART）
     */
    @Builder.Default
    @Column(value = "page_merge_mode", comment = "分页内容合并方式", length = 20)
    private String pageMergeMode = "APPEND";

    // ==================== 动态页面配置 ====================

    /**
     * 等待加载的选择器
     * 用于动态页面，等待特定元素加载完成的选择器
     */
    @Column(value = "wait_for_selector", comment = "等待加载的选择器", length = 200)
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     * 等待页面加载的时间
     */
    @Builder.Default
    @Column(value = "wait_time_millis", comment = "等待时间(毫秒)")
    private Integer waitTimeMillis = 3000;

    /**
     * 滚动加载配置
     * 是否需要滚动页面来加载内容
     */
    @Builder.Default
    @Column(value = "scroll_load_enabled", comment = "是否启用滚动加载")
    private Boolean scrollLoadEnabled = false;

    /**
     * 滚动距离
     * 每次滚动的像素距离
     */
    @Builder.Default
    @Column(value = "scroll_distance", comment = "滚动距离(像素)")
    private Integer scrollDistance = 1000;

    /**
     * 滚动间隔时间（毫秒）
     * 每次滚动之间的间隔时间
     */
    @Builder.Default
    @Column(value = "scroll_interval_millis", comment = "滚动间隔时间(毫秒)")
    private Integer scrollIntervalMillis = 2000;

    // ==================== 内容质量控制 ====================

    /**
     * 最小内容长度
     * 章节内容的最小长度限制（字符数）
     */
    @Builder.Default
    @Column(value = "min_content_length", comment = "最小内容长度")
    private Integer minContentLength = 100;

    /**
     * 最大内容长度
     * 章节内容的最大长度限制（字符数）
     */
    @Builder.Default
    @Column(value = "max_content_length", comment = "最大内容长度")
    private Integer maxContentLength = 50000;

    /**
     * 内容验证规则
     * 用于验证内容质量的规则
     */
    @Column(value = "content_validation_rules", comment = "内容验证规则(JSON格式)", type = "TEXT")
    private Map<String, String> contentValidationRules;

    /**
     * 重复内容检测
     * 是否检测和处理重复内容
     */
    @Builder.Default
    @Column(value = "duplicate_detection_enabled", comment = "是否启用重复内容检测")
    private Boolean duplicateDetectionEnabled = true;

    /**
     * 重复内容阈值
     * 重复内容的相似度阈值（0.0-1.0）
     */
    @Builder.Default
    @Column(value = "duplicate_threshold", comment = "重复内容阈值")
    private Double duplicateThreshold = 0.8;

    // ==================== 高级配置 ====================

    /**
     * 字符编码
     * 页面内容的字符编码
     */
    @Builder.Default
    @Column(value = "charset", comment = "字符编码", length = 20)
    private String charset = "UTF-8";

    /**
     * 自定义请求头
     * 访问内容页时使用的自定义请求头
     */
    @Column(value = "custom_headers", comment = "自定义请求头(JSON格式)", type = "TEXT")
    private Map<String, String> customHeaders;

    /**
     * 内容后处理脚本
     * 用于内容后处理的JavaScript脚本
     */
    @Column(value = "post_process_script", comment = "内容后处理脚本", type = "TEXT")
    private String postProcessScript;

    /**
     * 缓存配置
     * 是否缓存内容页面
     */
    @Builder.Default
    @Column(value = "cache_enabled", comment = "是否启用缓存")
    private Boolean cacheEnabled = false;

    /**
     * 缓存过期时间（秒）
     * 内容缓存的过期时间
     */
    @Builder.Default
    @Column(value = "cache_expire_seconds", comment = "缓存过期时间(秒)")
    private Integer cacheExpireSeconds = 3600;

    // ==================== 业务方法 ====================

    /**
     * 验证内容规则配置的完整性
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        return contentSelector != null && !contentSelector.trim().isEmpty();
    }

    /**
     * 检查是否需要等待页面加载
     * 
     * @return 是否需要等待
     */
    public boolean needsWaitForLoad() {
        return waitForSelector != null && !waitForSelector.trim().isEmpty();
    }

    /**
     * 检查是否启用了滚动加载
     * 
     * @return 是否启用滚动加载
     */
    public boolean isScrollLoadEnabled() {
        return Boolean.TRUE.equals(scrollLoadEnabled);
    }

    /**
     * 获取有效的等待时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 等待时间（毫秒）
     */
    public int getEffectiveWaitTimeMillis() {
        if (waitTimeMillis == null || waitTimeMillis <= 0) {
            return 3000; // 默认3秒
        }
        if (waitTimeMillis > 30000) {
            return 30000; // 最大30秒
        }
        return waitTimeMillis;
    }

    /**
     * 获取有效的页面间隔时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 页面间隔时间（毫秒）
     */
    public int getEffectivePageIntervalMillis() {
        if (pageIntervalMillis == null || pageIntervalMillis <= 0) {
            return 1000; // 默认1秒
        }
        if (pageIntervalMillis > 10000) {
            return 10000; // 最大10秒
        }
        return pageIntervalMillis;
    }

    /**
     * 获取有效的最大页数限制
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最大页数限制
     */
    public int getEffectiveMaxPageLimit() {
        if (maxPageLimit == null || maxPageLimit <= 0) {
            return 10; // 默认10页
        }
        if (maxPageLimit > 50) {
            return 50; // 最大50页
        }
        return maxPageLimit;
    }

    /**
     * 获取有效的最小内容长度
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最小内容长度
     */
    public int getEffectiveMinContentLength() {
        if (minContentLength == null || minContentLength <= 0) {
            return 100; // 默认100字符
        }
        return minContentLength;
    }

    /**
     * 获取有效的最大内容长度
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最大内容长度
     */
    public int getEffectiveMaxContentLength() {
        if (maxContentLength == null || maxContentLength <= 0) {
            return 50000; // 默认50000字符
        }
        return maxContentLength;
    }

    /**
     * 检查是否有内容过滤配置
     * 
     * @return 是否有内容过滤配置
     */
    public boolean hasContentFilter() {
        return (contentFilterRegex != null && !contentFilterRegex.trim().isEmpty()) ||
               (removeSelectors != null && !removeSelectors.isEmpty()) ||
               (replacementRules != null && !replacementRules.isEmpty());
    }

    /**
     * 检查是否有内容验证规则
     * 
     * @return 是否有内容验证规则
     */
    public boolean hasContentValidationRules() {
        return contentValidationRules != null && !contentValidationRules.isEmpty();
    }

    /**
     * 检查是否启用了重复内容检测
     * 
     * @return 是否启用重复内容检测
     */
    public boolean isDuplicateDetectionEnabled() {
        return Boolean.TRUE.equals(duplicateDetectionEnabled);
    }

    /**
     * 获取有效的重复内容阈值
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 重复内容阈值
     */
    public double getEffectiveDuplicateThreshold() {
        if (duplicateThreshold == null || duplicateThreshold < 0.0 || duplicateThreshold > 1.0) {
            return 0.8; // 默认0.8
        }
        return duplicateThreshold;
    }

    /**
     * 验证内容长度是否符合要求
     * 
     * @param content 内容文本
     * @return 是否符合要求
     */
    public boolean isValidContentLength(String content) {
        if (content == null) {
            return false;
        }
        int length = content.trim().length();
        return length >= getEffectiveMinContentLength() && length <= getEffectiveMaxContentLength();
    }

    /**
     * 获取指定字段的内容验证规则
     * 
     * @param fieldName 字段名
     * @return 验证规则，如果不存在返回null
     */
    public String getContentValidationRule(String fieldName) {
        if (contentValidationRules == null || fieldName == null) {
            return null;
        }
        return contentValidationRules.get(fieldName);
    }

    /**
     * 获取指定模式的替换规则
     * 
     * @param pattern 正则表达式模式
     * @return 替换文本，如果不存在返回null
     */
    public String getReplacementRule(String pattern) {
        if (replacementRules == null || pattern == null) {
            return null;
        }
        return replacementRules.get(pattern);
    }

    /**
     * 检查是否为追加合并模式
     * 
     * @return 是否为追加合并模式
     */
    public boolean isAppendMergeMode() {
        return "APPEND".equalsIgnoreCase(pageMergeMode);
    }

    /**
     * 检查是否为替换合并模式
     * 
     * @return 是否为替换合并模式
     */
    public boolean isReplaceMergeMode() {
        return "REPLACE".equalsIgnoreCase(pageMergeMode);
    }

    /**
     * 检查是否为智能合并模式
     * 
     * @return 是否为智能合并模式
     */
    public boolean isSmartMergeMode() {
        return "SMART".equalsIgnoreCase(pageMergeMode);
    }
}
