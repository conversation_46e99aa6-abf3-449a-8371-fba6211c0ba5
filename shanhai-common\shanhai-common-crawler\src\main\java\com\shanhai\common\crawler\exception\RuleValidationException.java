package com.shanhai.common.crawler.exception;

import java.util.List;
import java.util.Map;

/**
 * 规则验证异常
 * <p>
 * 专门用于处理爬虫规则验证失败的异常。
 * 提供详细的验证错误信息，支持多个验证错误的聚合显示。
 * 
 * <p>主要特性：
 * <ul>
 *   <li>支持多个验证错误的聚合</li>
 *   <li>提供字段级别的错误信息</li>
 *   <li>支持错误信息的分类和格式化</li>
 *   <li>便于前端展示和用户理解</li>
 * </ul>
 * 
 * <p>使用场景：
 * <ul>
 *   <li>规则配置验证失败</li>
 *   <li>字段格式验证错误</li>
 *   <li>业务规则检查失败</li>
 *   <li>配置一致性验证失败</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
public class RuleValidationException extends CrawlerException {

    private static final long serialVersionUID = 1L;

    /**
     * 验证错误列表
     */
    private final List<String> validationErrors;

    /**
     * 字段级别的错误信息
     * 键为字段名，值为错误信息列表
     */
    private final Map<String, List<String>> fieldErrors;

    /**
     * 验证失败的规则标识符
     */
    private final String ruleIdentifier;

    /**
     * 验证失败的规则名称
     */
    private final String ruleName;

    // ==================== 构造函数 ====================

    /**
     * 构造函数 - 单个验证错误
     * 
     * @param message 错误信息
     */
    public RuleValidationException(String message) {
        super(CrawlerErrorCode.RULE_VALIDATION_FAILED, message);
        this.validationErrors = null;
        this.fieldErrors = null;
        this.ruleIdentifier = null;
        this.ruleName = null;
    }

    /**
     * 构造函数 - 单个验证错误（带原因）
     * 
     * @param message 错误信息
     * @param cause 原因异常
     */
    public RuleValidationException(String message, Throwable cause) {
        super(CrawlerErrorCode.RULE_VALIDATION_FAILED, message, cause);
        this.validationErrors = null;
        this.fieldErrors = null;
        this.ruleIdentifier = null;
        this.ruleName = null;
    }

    /**
     * 构造函数 - 多个验证错误
     * 
     * @param validationErrors 验证错误列表
     */
    public RuleValidationException(List<String> validationErrors) {
        super(CrawlerErrorCode.RULE_VALIDATION_FAILED, buildMessage(validationErrors));
        this.validationErrors = validationErrors;
        this.fieldErrors = null;
        this.ruleIdentifier = null;
        this.ruleName = null;
    }

    /**
     * 构造函数 - 多个验证错误（带规则信息）
     * 
     * @param validationErrors 验证错误列表
     * @param ruleIdentifier 规则标识符
     * @param ruleName 规则名称
     */
    public RuleValidationException(List<String> validationErrors, String ruleIdentifier, String ruleName) {
        super(CrawlerErrorCode.RULE_VALIDATION_FAILED, buildMessage(validationErrors, ruleIdentifier, ruleName));
        this.validationErrors = validationErrors;
        this.fieldErrors = null;
        this.ruleIdentifier = ruleIdentifier;
        this.ruleName = ruleName;
    }

    /**
     * 构造函数 - 字段级别的验证错误
     * 
     * @param fieldErrors 字段错误信息
     * @param ruleIdentifier 规则标识符
     * @param ruleName 规则名称
     */
    public RuleValidationException(Map<String, List<String>> fieldErrors, String ruleIdentifier, String ruleName) {
        super(CrawlerErrorCode.RULE_VALIDATION_FAILED, buildMessage(fieldErrors, ruleIdentifier, ruleName));
        this.validationErrors = null;
        this.fieldErrors = fieldErrors;
        this.ruleIdentifier = ruleIdentifier;
        this.ruleName = ruleName;
    }

    /**
     * 构造函数 - 完整的验证错误信息
     * 
     * @param validationErrors 验证错误列表
     * @param fieldErrors 字段错误信息
     * @param ruleIdentifier 规则标识符
     * @param ruleName 规则名称
     */
    public RuleValidationException(List<String> validationErrors, Map<String, List<String>> fieldErrors, 
                                   String ruleIdentifier, String ruleName) {
        super(CrawlerErrorCode.RULE_VALIDATION_FAILED, buildMessage(validationErrors, fieldErrors, ruleIdentifier, ruleName));
        this.validationErrors = validationErrors;
        this.fieldErrors = fieldErrors;
        this.ruleIdentifier = ruleIdentifier;
        this.ruleName = ruleName;
    }

    // ==================== Getter方法 ====================

    /**
     * 获取验证错误列表
     * 
     * @return 验证错误列表
     */
    public List<String> getValidationErrors() {
        return validationErrors;
    }

    /**
     * 获取字段级别的错误信息
     * 
     * @return 字段错误信息
     */
    public Map<String, List<String>> getFieldErrors() {
        return fieldErrors;
    }

    /**
     * 获取规则标识符
     * 
     * @return 规则标识符
     */
    public String getRuleIdentifier() {
        return ruleIdentifier;
    }

    /**
     * 获取规则名称
     * 
     * @return 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    // ==================== 业务方法 ====================

    /**
     * 检查是否有验证错误
     * 
     * @return 是否有验证错误
     */
    public boolean hasValidationErrors() {
        return validationErrors != null && !validationErrors.isEmpty();
    }

    /**
     * 检查是否有字段错误
     * 
     * @return 是否有字段错误
     */
    public boolean hasFieldErrors() {
        return fieldErrors != null && !fieldErrors.isEmpty();
    }

    /**
     * 获取验证错误总数
     * 
     * @return 错误总数
     */
    public int getErrorCount() {
        int count = 0;
        
        if (validationErrors != null) {
            count += validationErrors.size();
        }
        
        if (fieldErrors != null) {
            count += fieldErrors.values().stream()
                    .mapToInt(List::size)
                    .sum();
        }
        
        return count;
    }

    /**
     * 获取指定字段的错误信息
     * 
     * @param fieldName 字段名
     * @return 字段错误信息列表
     */
    public List<String> getFieldErrors(String fieldName) {
        if (fieldErrors == null || fieldName == null) {
            return null;
        }
        return fieldErrors.get(fieldName);
    }

    /**
     * 检查指定字段是否有错误
     * 
     * @param fieldName 字段名
     * @return 是否有错误
     */
    public boolean hasFieldError(String fieldName) {
        List<String> errors = getFieldErrors(fieldName);
        return errors != null && !errors.isEmpty();
    }

    /**
     * 获取格式化的错误信息
     * 用于日志记录和调试
     * 
     * @return 格式化的错误信息
     */
    public String getFormattedErrorMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (ruleIdentifier != null || ruleName != null) {
            sb.append("规则验证失败");
            if (ruleIdentifier != null) {
                sb.append(" [").append(ruleIdentifier).append("]");
            }
            if (ruleName != null) {
                sb.append(" (").append(ruleName).append(")");
            }
            sb.append(":\n");
        } else {
            sb.append("规则验证失败:\n");
        }
        
        if (hasValidationErrors()) {
            sb.append("\n通用错误:\n");
            for (int i = 0; i < validationErrors.size(); i++) {
                sb.append("  ").append(i + 1).append(". ").append(validationErrors.get(i)).append("\n");
            }
        }
        
        if (hasFieldErrors()) {
            sb.append("\n字段错误:\n");
            fieldErrors.forEach((field, errors) -> {
                sb.append("  ").append(field).append(":\n");
                for (int i = 0; i < errors.size(); i++) {
                    sb.append("    - ").append(errors.get(i)).append("\n");
                }
            });
        }
        
        return sb.toString();
    }

    /**
     * 获取用户友好的错误信息
     * 用于前端显示
     * 
     * @return 用户友好的错误信息
     */
    public String getUserFriendlyMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (ruleName != null) {
            sb.append("规则 \"").append(ruleName).append("\" 配置有误");
        } else {
            sb.append("规则配置有误");
        }
        
        int errorCount = getErrorCount();
        if (errorCount > 0) {
            sb.append("，共发现 ").append(errorCount).append(" 个问题");
        }
        
        sb.append("，请检查并修正后重试。");
        
        return sb.toString();
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建单个验证错误的异常
     * 
     * @param message 错误信息
     * @return 验证异常
     */
    public static RuleValidationException single(String message) {
        return new RuleValidationException(message);
    }

    /**
     * 创建多个验证错误的异常
     * 
     * @param errors 错误列表
     * @return 验证异常
     */
    public static RuleValidationException multiple(List<String> errors) {
        return new RuleValidationException(errors);
    }

    /**
     * 创建字段验证错误的异常
     * 
     * @param fieldErrors 字段错误信息
     * @return 验证异常
     */
    public static RuleValidationException fields(Map<String, List<String>> fieldErrors) {
        return new RuleValidationException(fieldErrors, null, null);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建错误信息 - 验证错误列表
     * 
     * @param validationErrors 验证错误列表
     * @return 错误信息
     */
    private static String buildMessage(List<String> validationErrors) {
        if (validationErrors == null || validationErrors.isEmpty()) {
            return "规则验证失败";
        }
        
        if (validationErrors.size() == 1) {
            return "规则验证失败: " + validationErrors.get(0);
        }
        
        return "规则验证失败，共 " + validationErrors.size() + " 个错误: " + 
               String.join("; ", validationErrors);
    }

    /**
     * 构建错误信息 - 验证错误列表（带规则信息）
     * 
     * @param validationErrors 验证错误列表
     * @param ruleIdentifier 规则标识符
     * @param ruleName 规则名称
     * @return 错误信息
     */
    private static String buildMessage(List<String> validationErrors, String ruleIdentifier, String ruleName) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("规则验证失败");
        if (ruleIdentifier != null) {
            sb.append(" [").append(ruleIdentifier).append("]");
        }
        if (ruleName != null) {
            sb.append(" (").append(ruleName).append(")");
        }
        
        if (validationErrors != null && !validationErrors.isEmpty()) {
            sb.append(": ").append(String.join("; ", validationErrors));
        }
        
        return sb.toString();
    }

    /**
     * 构建错误信息 - 字段错误信息
     * 
     * @param fieldErrors 字段错误信息
     * @param ruleIdentifier 规则标识符
     * @param ruleName 规则名称
     * @return 错误信息
     */
    private static String buildMessage(Map<String, List<String>> fieldErrors, String ruleIdentifier, String ruleName) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("规则验证失败");
        if (ruleIdentifier != null) {
            sb.append(" [").append(ruleIdentifier).append("]");
        }
        if (ruleName != null) {
            sb.append(" (").append(ruleName).append(")");
        }
        
        if (fieldErrors != null && !fieldErrors.isEmpty()) {
            sb.append(": 字段验证错误");
            fieldErrors.forEach((field, errors) -> {
                sb.append("; ").append(field).append(": ").append(String.join(", ", errors));
            });
        }
        
        return sb.toString();
    }

    /**
     * 构建错误信息 - 完整的验证错误信息
     * 
     * @param validationErrors 验证错误列表
     * @param fieldErrors 字段错误信息
     * @param ruleIdentifier 规则标识符
     * @param ruleName 规则名称
     * @return 错误信息
     */
    private static String buildMessage(List<String> validationErrors, Map<String, List<String>> fieldErrors, 
                                       String ruleIdentifier, String ruleName) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("规则验证失败");
        if (ruleIdentifier != null) {
            sb.append(" [").append(ruleIdentifier).append("]");
        }
        if (ruleName != null) {
            sb.append(" (").append(ruleName).append(")");
        }
        
        boolean hasErrors = false;
        
        if (validationErrors != null && !validationErrors.isEmpty()) {
            sb.append(": ").append(String.join("; ", validationErrors));
            hasErrors = true;
        }
        
        if (fieldErrors != null && !fieldErrors.isEmpty()) {
            if (hasErrors) {
                sb.append("; ");
            } else {
                sb.append(": ");
            }
            sb.append("字段验证错误");
            fieldErrors.forEach((field, errors) -> {
                sb.append("; ").append(field).append(": ").append(String.join(", ", errors));
            });
        }
        
        return sb.toString();
    }
}
