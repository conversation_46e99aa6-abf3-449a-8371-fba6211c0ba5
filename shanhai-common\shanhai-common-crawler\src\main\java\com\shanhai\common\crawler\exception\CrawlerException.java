package com.shanhai.common.crawler.exception;

/**
 * 爬虫专用异常，支持错误码分级
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class CrawlerException extends ServiceException {
    /** 错误码 */
    private final CrawlerErrorCode errorCode;

    /**
     * 构造方法
     *
     * @param errorCode 错误码
     * @param message   异常信息
     */
    public CrawlerException(CrawlerErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造方法（带原因）
     *
     * @param errorCode 错误码
     * @param message   异常信息
     * @param cause     原因异常
     */
    public CrawlerException(CrawlerErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public CrawlerErrorCode getErrorCode() {
        return errorCode;
    }
} 