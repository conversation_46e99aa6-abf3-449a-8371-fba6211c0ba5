package com.shanhai.api.controller;

import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.service.NovelCrawlerRuleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 小说爬虫规则聚合管理控制器
 * <p>
 * 提供聚合式规则的增删改查、批量导入导出等REST接口。
 * 规则持久化支持多表/配置文件/远程等多数据源聚合。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/crawler/rule")
public class NovelCrawlerRuleController {
    @Resource
    private NovelCrawlerRuleService ruleService;

    /**
     * 聚合查询所有爬虫规则
     *
     * @return 规则列表
     */
    @GetMapping
    public List<CrawlerRuleNovel> list() {
        return ruleService.loadAllRules();
    }

    /**
     * 按唯一标识查询单个爬虫规则
     *
     * @param id 规则唯一标识（如 sourceId）
     * @return 规则详情
     */
    @GetMapping("/{id}")
    public CrawlerRuleNovel get(@PathVariable String id) {
        return ruleService.loadRuleById(id);
    }

    /**
     * 新增聚合爬虫规则
     *
     * @param rule 聚合规则对象
     * @return 是否成功
     */
    @PostMapping
    public boolean add(@RequestBody CrawlerRuleNovel rule) {
        return ruleService.saveRule(rule);
    }

    /**
     * 更新聚合爬虫规则
     *
     * @param rule 聚合规则对象
     * @return 是否成功
     */
    @PutMapping
    public boolean update(@RequestBody CrawlerRuleNovel rule) {
        return ruleService.updateRule(rule);
    }

    /**
     * 删除聚合爬虫规则
     *
     * @param id 规则唯一标识
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return ruleService.deleteRuleById(id);
    }

    /**
     * 批量导入规则（如JSON文件）
     *
     * @param rules 规则列表
     * @return 是否成功
     */
    @PostMapping("/import")
    public boolean importRules(@RequestBody List<CrawlerRuleNovel> rules) {
        return ruleService.importRules(rules);
    }

    /**
     * 批量导出所有规则
     *
     * @return 规则列表
     */
    @GetMapping("/export")
    public List<CrawlerRuleNovel> exportRules() {
        return ruleService.exportRules();
    }
} 