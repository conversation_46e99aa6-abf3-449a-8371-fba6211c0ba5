<template>
  <div class="rule-page">
    <el-card>
      <div slot="header">
        <span>规则管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">
          新增规则
        </el-button>
      </div>
      
      <el-table :data="ruleList" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="规则名称" />
        <el-table-column prop="type" label="规则类型" />
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '启用' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="mini" 
              :type="scope.row.status === '启用' ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === '启用' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Rule',
  data() {
    return {
      ruleList: [
        {
          id: 1,
          name: '起点中文网规则',
          type: '小说抓取',
          status: '启用',
          createTime: '2024-01-15 10:30'
        },
        {
          id: 2,
          name: '笔趣阁规则',
          type: '小说抓取',
          status: '启用',
          createTime: '2024-01-14 15:20'
        },
        {
          id: 3,
          name: '纵横中文网规则',
          type: '小说抓取',
          status: '禁用',
          createTime: '2024-01-13 09:45'
        }
      ]
    }
  },
  methods: {
    handleAdd() {
      this.$message.info('新增规则功能待实现')
    },
    handleEdit(row) {
      this.$message.info(`编辑规则：${row.name}`)
    },
    handleToggleStatus(row) {
      const newStatus = row.status === '启用' ? '禁用' : '启用'
      this.$confirm(`确定要${newStatus}规则"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.status = newStatus
        this.$message.success(`${newStatus}成功`)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除规则"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style scoped>
.rule-page {
  padding: 20px;
}
</style> 