package com.shanhai.common.crawler.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.config.CrawlerRuleAntiSpider;
import com.shanhai.common.crawler.repository.AntiSpiderMapper;
import com.shanhai.common.crawler.service.AntiSpiderService;
import org.springframework.stereotype.Service;

/**
 * 反爬虫配置Service实现
 */
@Service
public class AntiSpiderServiceImpl extends ServiceImpl<AntiSpiderMapper, CrawlerRuleAntiSpider> implements AntiSpiderService {
} 