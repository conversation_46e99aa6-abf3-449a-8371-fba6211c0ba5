package com.shanhai.common.crawler.domain.entity.rule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * 书籍信息规则配置实体
 * <p>
 * 定义如何从书籍详情页提取书籍基本信息的规则配置。
 * 包括书名、作者、封面、简介、状态等信息的提取规则。
 * 
 * <p>主要功能：
 * <ul>
 *   <li>配置书籍基本信息的提取规则</li>
 *   <li>支持多种数据格式的解析（HTML、JSON等）</li>
 *   <li>提供数据清洗和格式化功能</li>
 *   <li>支持动态页面的等待和加载</li>
 * </ul>
 * 
 * <p>支持提取的信息：
 * <ul>
 *   <li>基本信息：书名、作者、封面、简介</li>
 *   <li>状态信息：连载状态、更新时间、字数</li>
 *   <li>分类信息：类型、标签、评分</li>
 *   <li>链接信息：章节列表页链接</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_book_info", comment = "书籍信息规则配置表")
@TableName("crawler_rule_book_info")
public class BookInfoRuleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 关联的爬虫规则ID
     */
    @Column(value = "rule_id", comment = "关联的爬虫规则ID", length = 32)
    private String ruleId;

    // ==================== 基本信息提取配置 ====================

    /**
     * 书名选择器
     * 用于提取书籍名称的CSS选择器或JSONPath
     */
    @NotBlank(message = "书名选择器不能为空")
    @Column(value = "book_title_selector", comment = "书名选择器", length = 200)
    private String bookTitleSelector;

    /**
     * 作者选择器
     * 用于提取作者姓名的选择器
     */
    @Column(value = "author_selector", comment = "作者选择器", length = 200)
    private String authorSelector;

    /**
     * 封面图片选择器
     * 用于提取封面图片URL的选择器
     */
    @Column(value = "cover_image_selector", comment = "封面图片选择器", length = 200)
    private String coverImageSelector;

    /**
     * 封面图片属性名
     * 提取封面图片时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "cover_image_attribute", comment = "封面图片属性名", length = 20)
    private String coverImageAttribute = "src";

    /**
     * 书籍简介选择器
     * 用于提取书籍简介的选择器
     */
    @Column(value = "book_intro_selector", comment = "书籍简介选择器", length = 200)
    private String bookIntroSelector;

    // ==================== 状态信息提取配置 ====================

    /**
     * 书籍状态选择器
     * 用于提取书籍连载状态（连载中/已完结）的选择器
     */
    @Column(value = "book_status_selector", comment = "书籍状态选择器", length = 200)
    private String bookStatusSelector;

    /**
     * 更新时间选择器
     * 用于提取最后更新时间的选择器
     */
    @Column(value = "update_time_selector", comment = "更新时间选择器", length = 200)
    private String updateTimeSelector;

    /**
     * 字数选择器
     * 用于提取书籍总字数的选择器
     */
    @Column(value = "word_count_selector", comment = "字数选择器", length = 200)
    private String wordCountSelector;

    /**
     * 最新章节选择器
     * 用于提取最新章节标题的选择器
     */
    @Column(value = "latest_chapter_selector", comment = "最新章节选择器", length = 200)
    private String latestChapterSelector;

    /**
     * 最新章节链接选择器
     * 用于提取最新章节链接的选择器
     */
    @Column(value = "latest_chapter_url_selector", comment = "最新章节链接选择器", length = 200)
    private String latestChapterUrlSelector;

    /**
     * 最新章节链接属性名
     * 提取最新章节链接时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "latest_chapter_url_attribute", comment = "最新章节链接属性名", length = 20)
    private String latestChapterUrlAttribute = "href";

    // ==================== 分类信息提取配置 ====================

    /**
     * 书籍分类选择器
     * 用于提取书籍分类/类型的选择器
     */
    @Column(value = "book_category_selector", comment = "书籍分类选择器", length = 200)
    private String bookCategorySelector;

    /**
     * 书籍标签选择器
     * 用于提取书籍标签的选择器
     */
    @Column(value = "book_tags_selector", comment = "书籍标签选择器", length = 200)
    private String bookTagsSelector;

    /**
     * 评分选择器
     * 用于提取书籍评分的选择器
     */
    @Column(value = "book_rating_selector", comment = "评分选择器", length = 200)
    private String bookRatingSelector;

    /**
     * 评分人数选择器
     * 用于提取评分人数的选择器
     */
    @Column(value = "rating_count_selector", comment = "评分人数选择器", length = 200)
    private String ratingCountSelector;

    // ==================== 链接信息提取配置 ====================

    /**
     * 章节列表页链接选择器
     * 用于提取章节列表页链接的选择器
     */
    @Column(value = "chapter_list_url_selector", comment = "章节列表页链接选择器", length = 200)
    private String chapterListUrlSelector;

    /**
     * 章节列表页链接属性名
     * 提取章节列表页链接时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "chapter_list_url_attribute", comment = "章节列表页链接属性名", length = 20)
    private String chapterListUrlAttribute = "href";

    /**
     * 阅读页链接选择器
     * 用于提取开始阅读链接的选择器
     */
    @Column(value = "read_url_selector", comment = "阅读页链接选择器", length = 200)
    private String readUrlSelector;

    /**
     * 阅读页链接属性名
     * 提取阅读页链接时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "read_url_attribute", comment = "阅读页链接属性名", length = 20)
    private String readUrlAttribute = "href";

    // ==================== 数据处理配置 ====================

    /**
     * 需要移除的元素选择器
     * 用于移除不需要的HTML元素的选择器
     */
    @Column(value = "remove_elements_selector", comment = "需要移除的元素选择器", length = 500)
    private String removeElementsSelector;

    /**
     * 简介内容过滤正则表达式
     * 用于清理简介内容的正则表达式
     */
    @Column(value = "intro_filter_regex", comment = "简介内容过滤正则表达式", length = 500)
    private String introFilterRegex;

    /**
     * 是否保留HTML格式
     * 在提取简介等内容时是否保留HTML格式
     */
    @Builder.Default
    @Column(value = "keep_html_format", comment = "是否保留HTML格式")
    private Boolean keepHtmlFormat = false;

    /**
     * 文本长度限制
     * 提取文本内容的最大长度限制
     */
    @Column(value = "text_length_limit", comment = "文本长度限制")
    private Integer textLengthLimit;

    // ==================== 动态页面配置 ====================

    /**
     * 是否需要登录
     * 访问书籍详情页是否需要登录
     */
    @Builder.Default
    @Column(value = "login_required", comment = "是否需要登录")
    private Boolean loginRequired = false;

    /**
     * 等待加载的选择器
     * 用于动态页面，等待特定元素加载完成的选择器
     */
    @Column(value = "wait_for_selector", comment = "等待加载的选择器", length = 200)
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     * 等待页面加载的时间
     */
    @Builder.Default
    @Column(value = "wait_time_millis", comment = "等待时间(毫秒)")
    private Integer waitTimeMillis = 3000;

    /**
     * 页面加载超时时间（毫秒）
     * 页面加载的超时时间
     */
    @Builder.Default
    @Column(value = "page_load_timeout_millis", comment = "页面加载超时时间(毫秒)")
    private Integer pageLoadTimeoutMillis = 30000;

    // ==================== 高级配置 ====================

    /**
     * 字符编码
     * 页面内容的字符编码
     */
    @Builder.Default
    @Column(value = "charset", comment = "字符编码", length = 20)
    private String charset = "UTF-8";

    /**
     * 自定义请求头
     * 访问书籍详情页时使用的自定义请求头
     */
    @Column(value = "custom_headers", comment = "自定义请求头(JSON格式)", type = "TEXT")
    private Map<String, String> customHeaders;

    /**
     * 数据映射规则
     * 用于字段名映射和数据转换的规则配置
     */
    @Column(value = "data_mapping_rules", comment = "数据映射规则(JSON格式)", type = "TEXT")
    private Map<String, String> dataMappingRules;

    /**
     * 验证规则
     * 用于验证提取数据有效性的规则
     */
    @Column(value = "validation_rules", comment = "验证规则(JSON格式)", type = "TEXT")
    private Map<String, String> validationRules;

    // ==================== 业务方法 ====================

    /**
     * 验证书籍信息规则配置的完整性
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        return bookTitleSelector != null && !bookTitleSelector.trim().isEmpty();
    }

    /**
     * 检查是否需要等待页面加载
     * 
     * @return 是否需要等待
     */
    public boolean needsWaitForLoad() {
        return waitForSelector != null && !waitForSelector.trim().isEmpty();
    }

    /**
     * 获取有效的等待时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 等待时间（毫秒）
     */
    public int getEffectiveWaitTimeMillis() {
        if (waitTimeMillis == null || waitTimeMillis <= 0) {
            return 3000; // 默认3秒
        }
        if (waitTimeMillis > 30000) {
            return 30000; // 最大30秒
        }
        return waitTimeMillis;
    }

    /**
     * 获取有效的页面加载超时时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 页面加载超时时间（毫秒）
     */
    public int getEffectivePageLoadTimeoutMillis() {
        if (pageLoadTimeoutMillis == null || pageLoadTimeoutMillis <= 0) {
            return 30000; // 默认30秒
        }
        if (pageLoadTimeoutMillis > 120000) {
            return 120000; // 最大2分钟
        }
        return pageLoadTimeoutMillis;
    }

    /**
     * 获取有效的文本长度限制
     * 如果未设置，返回默认值
     * 
     * @return 文本长度限制
     */
    public int getEffectiveTextLengthLimit() {
        if (textLengthLimit == null || textLengthLimit <= 0) {
            return 10000; // 默认10000字符
        }
        return textLengthLimit;
    }

    /**
     * 检查是否有移除元素的配置
     * 
     * @return 是否有移除元素配置
     */
    public boolean hasRemoveElementsConfig() {
        return removeElementsSelector != null && !removeElementsSelector.trim().isEmpty();
    }

    /**
     * 检查是否有简介过滤配置
     * 
     * @return 是否有简介过滤配置
     */
    public boolean hasIntroFilterConfig() {
        return introFilterRegex != null && !introFilterRegex.trim().isEmpty();
    }

    /**
     * 检查是否有数据映射规则
     * 
     * @return 是否有数据映射规则
     */
    public boolean hasDataMappingRules() {
        return dataMappingRules != null && !dataMappingRules.isEmpty();
    }

    /**
     * 检查是否有验证规则
     * 
     * @return 是否有验证规则
     */
    public boolean hasValidationRules() {
        return validationRules != null && !validationRules.isEmpty();
    }

    /**
     * 检查是否配置了章节列表页链接提取
     * 
     * @return 是否配置了章节列表页链接提取
     */
    public boolean hasChapterListUrlConfig() {
        return chapterListUrlSelector != null && !chapterListUrlSelector.trim().isEmpty();
    }

    /**
     * 检查是否配置了最新章节信息提取
     * 
     * @return 是否配置了最新章节信息提取
     */
    public boolean hasLatestChapterConfig() {
        return latestChapterSelector != null && !latestChapterSelector.trim().isEmpty();
    }

    /**
     * 检查是否配置了评分信息提取
     * 
     * @return 是否配置了评分信息提取
     */
    public boolean hasRatingConfig() {
        return bookRatingSelector != null && !bookRatingSelector.trim().isEmpty();
    }

    /**
     * 检查是否配置了分类信息提取
     * 
     * @return 是否配置了分类信息提取
     */
    public boolean hasCategoryConfig() {
        return bookCategorySelector != null && !bookCategorySelector.trim().isEmpty();
    }

    /**
     * 获取指定字段的数据映射规则
     * 
     * @param fieldName 字段名
     * @return 映射规则，如果不存在返回null
     */
    public String getDataMappingRule(String fieldName) {
        if (dataMappingRules == null || fieldName == null) {
            return null;
        }
        return dataMappingRules.get(fieldName);
    }

    /**
     * 获取指定字段的验证规则
     * 
     * @param fieldName 字段名
     * @return 验证规则，如果不存在返回null
     */
    public String getValidationRule(String fieldName) {
        if (validationRules == null || fieldName == null) {
            return null;
        }
        return validationRules.get(fieldName);
    }
}
