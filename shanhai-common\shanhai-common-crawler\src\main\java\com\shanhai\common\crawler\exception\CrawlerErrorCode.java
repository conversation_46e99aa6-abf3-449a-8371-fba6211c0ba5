package com.shanhai.common.crawler.exception;

/**
 * 爬虫错误码枚举
 * <p>
 * 定义爬虫模块中所有可能出现的错误类型和对应的错误码
 *
 * <AUTHOR>
 */
public enum CrawlerErrorCode {

    // 参数错误 (1000-1099)
    INVALID_PARAMETER(1000, "参数错误"),
    MISSING_REQUIRED_PARAMETER(1001, "缺少必需参数"),
    PARAMETER_FORMAT_ERROR(1002, "参数格式错误"),
    PARAMETER_OUT_OF_RANGE(1003, "参数超出范围"),

    // 规则配置错误 (1100-1199)
    RULE_NOT_FOUND(1100, "规则未找到"),
    RULE_CONFIG_ERROR(1101, "规则配置错误"),
    RULE_VALIDATION_FAILED(1102, "规则验证失败"),
    RULE_PARSE_ERROR(1103, "规则解析错误"),

    // 网络错误 (1200-1299)
    NETWORK_ERROR(1200, "网络连接错误"),
    NETWORK_TIMEOUT(1201, "网络连接超时"),
    HTTP_ERROR(1202, "HTTP请求错误"),
    CONNECTION_REFUSED(1203, "连接被拒绝"),
    DNS_RESOLUTION_FAILED(1204, "DNS解析失败"),

    // 解析错误 (1300-1399)
    PARSE_ERROR(1300, "页面解析错误"),
    SELECTOR_NOT_FOUND(1301, "选择器未找到匹配元素"),
    DATA_FORMAT_ERROR(1302, "数据格式错误"),
    CONTENT_EMPTY(1303, "内容为空"),
    ENCODING_ERROR(1304, "编码错误"),

    // 反爬虫相关 (1400-1499)
    ANTI_SPIDER_DETECTED(1400, "检测到反爬虫机制"),
    IP_BLOCKED(1401, "IP被封禁"),
    CAPTCHA_REQUIRED(1402, "需要验证码"),
    ACCESS_DENIED(1403, "访问被拒绝"),
    RATE_LIMIT_EXCEEDED(1404, "请求频率超限"),

    // 业务逻辑错误 (1500-1599)
    BOOK_NOT_FOUND(1500, "书籍未找到"),
    CHAPTER_NOT_FOUND(1501, "章节未找到"),
    CONTENT_NOT_AVAILABLE(1502, "内容不可用"),
    SOURCE_NOT_SUPPORTED(1503, "不支持的数据源"),
    OPERATION_NOT_ALLOWED(1504, "操作不被允许"),

    // 系统错误 (1600-1699)
    SYSTEM_ERROR(1600, "系统错误"),
    DATABASE_ERROR(1601, "数据库错误"),
    FILE_IO_ERROR(1602, "文件IO错误"),
    THREAD_INTERRUPTED(1603, "线程被中断"),
    RESOURCE_EXHAUSTED(1604, "资源耗尽"),

    // 配置错误 (1700-1799)
    CONFIG_NOT_FOUND(1700, "配置未找到"),
    CONFIG_PARSE_ERROR(1701, "配置解析错误"),
    CONFIG_VALIDATION_ERROR(1702, "配置验证错误"),
    CONFIG_LOAD_ERROR(1703, "配置加载错误"),

    // 策略错误 (1800-1899)
    STRATEGY_NOT_FOUND(1800, "策略未找到"),
    STRATEGY_EXECUTION_ERROR(1801, "策略执行错误"),
    STRATEGY_TIMEOUT(1802, "策略执行超时"),
    STRATEGY_INTERRUPTED(1803, "策略执行被中断"),

    // 兼容旧版本
    ANTI_SPIDER_BLOCKED(1400, "被反爬拦截"),
    CONFIG_ERROR(1700, "配置错误"),

    // 未知错误 (9999)
    UNKNOWN_ERROR(9999, "未知错误");

    private final int code;
    private final String message;

    CrawlerErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 获取错误分类
     */
    public ErrorCategory getCategory() {
        if (code >= 1000 && code < 1100) return ErrorCategory.PARAMETER;
        if (code >= 1100 && code < 1200) return ErrorCategory.RULE_CONFIG;
        if (code >= 1200 && code < 1300) return ErrorCategory.NETWORK;
        if (code >= 1300 && code < 1400) return ErrorCategory.PARSING;
        if (code >= 1400 && code < 1500) return ErrorCategory.ANTI_SPIDER;
        if (code >= 1500 && code < 1600) return ErrorCategory.BUSINESS;
        if (code >= 1600 && code < 1700) return ErrorCategory.SYSTEM;
        if (code >= 1700 && code < 1800) return ErrorCategory.CONFIG;
        if (code >= 1800 && code < 1900) return ErrorCategory.STRATEGY;
        return ErrorCategory.UNKNOWN;
    }

    /**
     * 获取错误级别
     */
    public ErrorLevel getLevel() {
        switch (getCategory()) {
            case SYSTEM:
                return ErrorLevel.CRITICAL;
            case NETWORK:
            case ANTI_SPIDER:
                return ErrorLevel.WARNING;
            case PARSING:
            case BUSINESS:
            case STRATEGY:
                return ErrorLevel.ERROR;
            case PARAMETER:
            case RULE_CONFIG:
            case CONFIG:
                return ErrorLevel.INFO;
            case UNKNOWN:
            default:
                return ErrorLevel.ERROR;
        }
    }

    /**
     * 根据错误码获取枚举
     */
    public static CrawlerErrorCode fromCode(int code) {
        for (CrawlerErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return UNKNOWN_ERROR;
    }

    /**
     * 判断是否为可重试的错误
     */
    public boolean isRetryable() {
        switch (this) {
            case NETWORK_ERROR:
            case NETWORK_TIMEOUT:
            case HTTP_ERROR:
            case CONNECTION_REFUSED:
            case DNS_RESOLUTION_FAILED:
            case RATE_LIMIT_EXCEEDED:
            case SYSTEM_ERROR:
            case DATABASE_ERROR:
            case FILE_IO_ERROR:
            case RESOURCE_EXHAUSTED:
            case STRATEGY_TIMEOUT:
            case STRATEGY_INTERRUPTED:
                return true;
            default:
                return false;
        }
    }

    /**
     * 获取建议的重试延迟时间（毫秒）
     */
    public long getRetryDelay() {
        switch (this) {
            case RATE_LIMIT_EXCEEDED:
                return 5000L;
            case NETWORK_TIMEOUT:
            case CONNECTION_REFUSED:
                return 3000L;
            case NETWORK_ERROR:
            case HTTP_ERROR:
                return 2000L;
            case SYSTEM_ERROR:
            case DATABASE_ERROR:
                return 1000L;
            default:
                return 500L;
        }
    }

    /**
     * 获取最大重试次数
     */
    public int getMaxRetries() {
        switch (this) {
            case RATE_LIMIT_EXCEEDED:
                return 5;
            case NETWORK_ERROR:
            case NETWORK_TIMEOUT:
            case HTTP_ERROR:
                return 3;
            case SYSTEM_ERROR:
            case DATABASE_ERROR:
                return 2;
            default:
                return 1;
        }
    }

    @Override
    public String toString() {
        return String.format("CrawlerErrorCode{code=%d, message='%s', category=%s, level=%s}",
            code, message, getCategory(), getLevel());
    }

    /**
     * 错误分类枚举
     */
    public enum ErrorCategory {
        PARAMETER("参数错误"),
        RULE_CONFIG("规则配置错误"),
        NETWORK("网络错误"),
        PARSING("解析错误"),
        ANTI_SPIDER("反爬虫错误"),
        BUSINESS("业务错误"),
        SYSTEM("系统错误"),
        CONFIG("配置错误"),
        STRATEGY("策略错误"),
        UNKNOWN("未知错误");

        private final String description;

        ErrorCategory(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 错误级别枚举
     */
    public enum ErrorLevel {
        INFO("信息", 1),
        WARNING("警告", 2),
        ERROR("错误", 3),
        CRITICAL("严重", 4);

        private final String description;
        private final int severity;

        ErrorLevel(String description, int severity) {
            this.description = description;
            this.severity = severity;
        }

        public String getDescription() {
            return description;
        }

        public int getSeverity() {
            return severity;
        }
    }
}