<template>
  <el-tooltip content="自定义列显示/顺序" placement="top">
    <el-dropdown ref="dropdown">
      <el-button type="primary" icon="el-icon-setting" circle></el-button>
      <el-dropdown-menu slot="dropdown" class="column-dropdown-menu">
        <div class="column-dropdown-actions">
          <el-button size="mini" @click.stop="$emit('show-all')" type="success" plain>全部显示</el-button>
          <el-button size="mini" @click.stop="$emit('hide-all')" type="warning" plain>全部隐藏</el-button>
        </div>
        <draggable v-model="localColumns" handle=".drag-handle" :animation="200" @end="updateColumns">
          <div v-for="col in localColumns" :key="col.prop" class="column-dropdown-item">
            <i class="el-icon-rank drag-handle" style="cursor:move;margin-right:6px;"></i>
            <el-checkbox v-model="col.visible">{{ col.label }}</el-checkbox>
          </div>
        </draggable>
      </el-dropdown-menu>
    </el-dropdown>
  </el-tooltip>
</template>
<script>
import draggable from 'vuedraggable'
export default {
  name: 'UserColumnSetting',
  components: { draggable },
  props: {
    columns: { type: Array, required: true }
  },
  data() {
    return {
      localColumns: JSON.parse(JSON.stringify(this.columns))
    }
  },
  watch: {
    columns: {
      handler(val) {
        this.localColumns = JSON.parse(JSON.stringify(val));
      },
      deep: true
    },
    localColumns: {
      handler(val) {
        this.$emit('update:columns', JSON.parse(JSON.stringify(val)));
      },
      deep: true
    }
  },
  methods: {
    updateColumns() {
      this.$emit('update:columns', JSON.parse(JSON.stringify(this.localColumns)));
    }
  }
}
</script>
<style scoped>
.column-dropdown-menu {
  padding: 10px 12px 6px 12px;
  min-width: 150px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.08);
  border: none;
}
.column-dropdown-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  gap: 6px;
}
.column-dropdown-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  padding: 2px 0 2px 2px;
  border-radius: 5px;
  transition: background 0.2s;
}
.column-dropdown-item:hover {
  background: #f5f7fa;
}
.drag-handle {
  color: #bbb;
  font-size: 15px;
  cursor: move;
}
</style> 