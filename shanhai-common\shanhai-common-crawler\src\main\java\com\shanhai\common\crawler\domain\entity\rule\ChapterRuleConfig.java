package com.shanhai.common.crawler.domain.entity.rule;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.Map;

/**
 * 章节规则配置实体
 * <p>
 * 定义如何从章节列表页提取章节信息的规则配置。
 * 包括章节标题、链接、发布时间等信息的提取规则。
 * 
 * <p>主要功能：
 * <ul>
 *   <li>配置章节列表的提取规则</li>
 *   <li>支持分页章节列表的处理</li>
 *   <li>提供章节排序和过滤功能</li>
 *   <li>支持动态加载的章节列表</li>
 * </ul>
 * 
 * <p>支持的章节信息：
 * <ul>
 *   <li>基本信息：章节标题、章节链接</li>
 *   <li>时间信息：发布时间、更新时间</li>
 *   <li>状态信息：是否免费、是否VIP</li>
 *   <li>排序信息：章节序号、排序权重</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_chapter", comment = "章节规则配置表")
@TableName("crawler_rule_chapter")
public class ChapterRuleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 关联的爬虫规则ID
     */
    @Column(value = "rule_id", comment = "关联的爬虫规则ID", length = 32)
    private String ruleId;

    // ==================== 章节列表提取配置 ====================

    /**
     * 章节列表容器选择器
     * 用于选择包含所有章节的容器元素的CSS选择器或JSONPath
     */
    @NotBlank(message = "章节列表容器选择器不能为空")
    @Column(value = "chapter_list_container_selector", comment = "章节列表容器选择器", length = 200)
    private String chapterListContainerSelector;

    /**
     * 单个章节项选择器
     * 用于选择单个章节项的选择器（相对于容器）
     */
    @NotBlank(message = "单个章节项选择器不能为空")
    @Column(value = "chapter_item_selector", comment = "单个章节项选择器", length = 200)
    private String chapterItemSelector;

    /**
     * 章节标题选择器
     * 用于提取章节标题的选择器（相对于章节项）
     */
    @NotBlank(message = "章节标题选择器不能为空")
    @Column(value = "chapter_title_selector", comment = "章节标题选择器", length = 200)
    private String chapterTitleSelector;

    /**
     * 章节链接选择器
     * 用于提取章节链接的选择器（相对于章节项）
     */
    @NotBlank(message = "章节链接选择器不能为空")
    @Column(value = "chapter_url_selector", comment = "章节链接选择器", length = 200)
    private String chapterUrlSelector;

    /**
     * 章节链接属性名
     * 提取章节链接时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "chapter_url_attribute", comment = "章节链接属性名", length = 20)
    private String chapterUrlAttribute = "href";

    // ==================== 章节附加信息提取配置 ====================

    /**
     * 章节发布时间选择器
     * 用于提取章节发布时间的选择器
     */
    @Column(value = "publish_time_selector", comment = "章节发布时间选择器", length = 200)
    private String publishTimeSelector;

    /**
     * 章节更新时间选择器
     * 用于提取章节更新时间的选择器
     */
    @Column(value = "update_time_selector", comment = "章节更新时间选择器", length = 200)
    private String updateTimeSelector;

    /**
     * 章节序号选择器
     * 用于提取章节序号的选择器
     */
    @Column(value = "chapter_index_selector", comment = "章节序号选择器", length = 200)
    private String chapterIndexSelector;

    /**
     * 章节字数选择器
     * 用于提取章节字数的选择器
     */
    @Column(value = "chapter_word_count_selector", comment = "章节字数选择器", length = 200)
    private String chapterWordCountSelector;

    /**
     * 章节状态选择器
     * 用于提取章节状态（免费/VIP/付费）的选择器
     */
    @Column(value = "chapter_status_selector", comment = "章节状态选择器", length = 200)
    private String chapterStatusSelector;

    /**
     * 章节价格选择器
     * 用于提取章节价格的选择器
     */
    @Column(value = "chapter_price_selector", comment = "章节价格选择器", length = 200)
    private String chapterPriceSelector;

    // ==================== 排序和过滤配置 ====================

    /**
     * 是否反向排序
     * 章节列表是否需要反向排序（通常网站显示的是倒序）
     */
    @Builder.Default
    @Column(value = "reverse_order", comment = "是否反向排序")
    private Boolean reverseOrder = false;

    /**
     * 排序字段
     * 用于排序的字段名（index、title、publishTime等）
     */
    @Builder.Default
    @Column(value = "sort_field", comment = "排序字段", length = 50)
    private String sortField = "index";

    /**
     * 排序方向
     * 排序方向（ASC/DESC）
     */
    @Builder.Default
    @Column(value = "sort_direction", comment = "排序方向", length = 10)
    private String sortDirection = "ASC";

    /**
     * 章节过滤正则表达式
     * 用于过滤章节标题的正则表达式
     */
    @Column(value = "chapter_filter_regex", comment = "章节过滤正则表达式", length = 500)
    private String chapterFilterRegex;

    /**
     * 排除章节关键词
     * 包含这些关键词的章节将被排除
     */
    @Column(value = "exclude_keywords", comment = "排除章节关键词", length = 500)
    private String excludeKeywords;

    /**
     * 最小章节标题长度
     * 章节标题的最小长度限制
     */
    @Builder.Default
    @Column(value = "min_title_length", comment = "最小章节标题长度")
    private Integer minTitleLength = 1;

    /**
     * 最大章节标题长度
     * 章节标题的最大长度限制
     */
    @Builder.Default
    @Column(value = "max_title_length", comment = "最大章节标题长度")
    private Integer maxTitleLength = 200;

    // ==================== 分页配置 ====================

    /**
     * 是否支持分页
     * 章节列表是否分页显示
     */
    @Builder.Default
    @Column(value = "pagination_enabled", comment = "是否支持分页")
    private Boolean paginationEnabled = false;

    /**
     * 下一页选择器
     * 用于获取下一页链接的选择器
     */
    @Column(value = "next_page_selector", comment = "下一页选择器", length = 200)
    private String nextPageSelector;

    /**
     * 下一页链接属性名
     * 提取下一页链接时使用的HTML属性名
     */
    @Builder.Default
    @Column(value = "next_page_attribute", comment = "下一页链接属性名", length = 20)
    private String nextPageAttribute = "href";

    /**
     * 最大页数限制
     * 章节列表的最大页数限制
     */
    @Builder.Default
    @Min(value = 1, message = "最大页数不能小于1")
    @Max(value = 1000, message = "最大页数不能大于1000")
    @Column(value = "max_page_limit", comment = "最大页数限制")
    private Integer maxPageLimit = 50;

    /**
     * 页面间隔时间（毫秒）
     * 访问分页时的间隔时间
     */
    @Builder.Default
    @Column(value = "page_interval_millis", comment = "页面间隔时间(毫秒)")
    private Integer pageIntervalMillis = 1000;

    // ==================== 动态页面配置 ====================

    /**
     * 等待加载的选择器
     * 用于动态页面，等待特定元素加载完成的选择器
     */
    @Column(value = "wait_for_selector", comment = "等待加载的选择器", length = 200)
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     * 等待页面加载的时间
     */
    @Builder.Default
    @Column(value = "wait_time_millis", comment = "等待时间(毫秒)")
    private Integer waitTimeMillis = 3000;

    /**
     * 滚动加载配置
     * 是否需要滚动页面来加载更多章节
     */
    @Builder.Default
    @Column(value = "scroll_load_enabled", comment = "是否启用滚动加载")
    private Boolean scrollLoadEnabled = false;

    /**
     * 滚动次数
     * 滚动加载的最大次数
     */
    @Builder.Default
    @Column(value = "scroll_count", comment = "滚动次数")
    private Integer scrollCount = 5;

    /**
     * 滚动间隔时间（毫秒）
     * 每次滚动之间的间隔时间
     */
    @Builder.Default
    @Column(value = "scroll_interval_millis", comment = "滚动间隔时间(毫秒)")
    private Integer scrollIntervalMillis = 2000;

    // ==================== 高级配置 ====================

    /**
     * 字符编码
     * 页面内容的字符编码
     */
    @Builder.Default
    @Column(value = "charset", comment = "字符编码", length = 20)
    private String charset = "UTF-8";

    /**
     * 自定义请求头
     * 访问章节列表页时使用的自定义请求头
     */
    @Column(value = "custom_headers", comment = "自定义请求头(JSON格式)", type = "TEXT")
    private Map<String, String> customHeaders;

    /**
     * 数据清洗规则
     * 用于清洗章节数据的规则配置
     */
    @Column(value = "data_cleaning_rules", comment = "数据清洗规则(JSON格式)", type = "TEXT")
    private Map<String, String> dataCleaningRules;

    /**
     * 去重配置
     * 是否对章节进行去重处理
     */
    @Builder.Default
    @Column(value = "deduplication_enabled", comment = "是否启用去重")
    private Boolean deduplicationEnabled = true;

    /**
     * 去重字段
     * 用于去重的字段名
     */
    @Builder.Default
    @Column(value = "deduplication_field", comment = "去重字段", length = 50)
    private String deduplicationField = "url";

    // ==================== 业务方法 ====================

    /**
     * 验证章节规则配置的完整性
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        return chapterListContainerSelector != null && !chapterListContainerSelector.trim().isEmpty()
            && chapterItemSelector != null && !chapterItemSelector.trim().isEmpty()
            && chapterTitleSelector != null && !chapterTitleSelector.trim().isEmpty()
            && chapterUrlSelector != null && !chapterUrlSelector.trim().isEmpty();
    }

    /**
     * 检查是否需要等待页面加载
     * 
     * @return 是否需要等待
     */
    public boolean needsWaitForLoad() {
        return waitForSelector != null && !waitForSelector.trim().isEmpty();
    }

    /**
     * 检查是否启用了滚动加载
     * 
     * @return 是否启用滚动加载
     */
    public boolean isScrollLoadEnabled() {
        return Boolean.TRUE.equals(scrollLoadEnabled);
    }

    /**
     * 获取有效的等待时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 等待时间（毫秒）
     */
    public int getEffectiveWaitTimeMillis() {
        if (waitTimeMillis == null || waitTimeMillis <= 0) {
            return 3000; // 默认3秒
        }
        if (waitTimeMillis > 30000) {
            return 30000; // 最大30秒
        }
        return waitTimeMillis;
    }

    /**
     * 获取有效的页面间隔时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 页面间隔时间（毫秒）
     */
    public int getEffectivePageIntervalMillis() {
        if (pageIntervalMillis == null || pageIntervalMillis <= 0) {
            return 1000; // 默认1秒
        }
        if (pageIntervalMillis > 10000) {
            return 10000; // 最大10秒
        }
        return pageIntervalMillis;
    }

    /**
     * 获取有效的滚动间隔时间
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 滚动间隔时间（毫秒）
     */
    public int getEffectiveScrollIntervalMillis() {
        if (scrollIntervalMillis == null || scrollIntervalMillis <= 0) {
            return 2000; // 默认2秒
        }
        if (scrollIntervalMillis > 10000) {
            return 10000; // 最大10秒
        }
        return scrollIntervalMillis;
    }

    /**
     * 获取有效的滚动次数
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 滚动次数
     */
    public int getEffectiveScrollCount() {
        if (scrollCount == null || scrollCount <= 0) {
            return 5; // 默认5次
        }
        if (scrollCount > 20) {
            return 20; // 最大20次
        }
        return scrollCount;
    }

    /**
     * 获取有效的最大页数限制
     * 如果未设置或设置不合理，返回默认值
     * 
     * @return 最大页数限制
     */
    public int getEffectiveMaxPageLimit() {
        if (maxPageLimit == null || maxPageLimit <= 0) {
            return 50; // 默认50页
        }
        if (maxPageLimit > 1000) {
            return 1000; // 最大1000页
        }
        return maxPageLimit;
    }

    /**
     * 检查是否有章节过滤配置
     * 
     * @return 是否有章节过滤配置
     */
    public boolean hasChapterFilter() {
        return (chapterFilterRegex != null && !chapterFilterRegex.trim().isEmpty()) ||
               (excludeKeywords != null && !excludeKeywords.trim().isEmpty());
    }

    /**
     * 检查是否有数据清洗规则
     * 
     * @return 是否有数据清洗规则
     */
    public boolean hasDataCleaningRules() {
        return dataCleaningRules != null && !dataCleaningRules.isEmpty();
    }

    /**
     * 检查是否需要反向排序
     * 
     * @return 是否需要反向排序
     */
    public boolean needsReverseOrder() {
        return Boolean.TRUE.equals(reverseOrder);
    }

    /**
     * 检查是否为降序排序
     * 
     * @return 是否为降序排序
     */
    public boolean isDescendingSort() {
        return "DESC".equalsIgnoreCase(sortDirection);
    }

    /**
     * 获取指定字段的数据清洗规则
     * 
     * @param fieldName 字段名
     * @return 清洗规则，如果不存在返回null
     */
    public String getDataCleaningRule(String fieldName) {
        if (dataCleaningRules == null || fieldName == null) {
            return null;
        }
        return dataCleaningRules.get(fieldName);
    }

    /**
     * 验证章节标题长度是否符合要求
     * 
     * @param title 章节标题
     * @return 是否符合要求
     */
    public boolean isValidTitleLength(String title) {
        if (title == null) {
            return false;
        }
        int length = title.trim().length();
        return length >= minTitleLength && length <= maxTitleLength;
    }
}
