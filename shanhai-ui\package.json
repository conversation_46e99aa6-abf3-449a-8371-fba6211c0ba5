{"name": "shan<PERSON>-ui", "version": "2.0.0", "description": "山海小说爬虫系统前端管理界面", "author": "shan<PERSON>", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "cross-env NODE_ENV=development PORT=8082 vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode production", "preview": "vue-cli-service serve --mode production", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "analyze": "vue-cli-service build --report"}, "dependencies": {"axios": "^1.6.7", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "nprogress": "^0.2.0", "vue": "^2.6.14", "vue-router": "^3.6.5", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-standard": "^8.0.1", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^9.20.1", "sass": "^1.70.0", "sass-loader": "^14.1.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}