package com.shanhai.api.controller;

import com.shanhai.api.controller.dto.NovelCrawlerDTO;
import com.shanhai.api.controller.validator.NovelCrawlerValidator;
import com.shanhai.common.core.result.R;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.service.crawler.NovelCrawlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小说爬虫控制器
 * <p>
 * 负责接收前端请求，参数校验，调用业务服务，返回统一响应。
 * 主要功能：
 * 1. 搜索小说书籍
 * 2. 获取书籍详情
 * 3. 获取章节列表
 * 4. 获取章节内容
 * 5. 批量获取章节内容
 * 6. 获取爬虫状态
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/novel/crawler")
public class NovelCrawlerController {

    private final NovelCrawlerService novelCrawlerService;
    private final NovelCrawlerValidator validator;

    @Autowired
    public NovelCrawlerController(NovelCrawlerService novelCrawlerService, NovelCrawlerValidator validator) {
        this.novelCrawlerService = novelCrawlerService;
        this.validator = validator;
    }

    /**
     * 获取应用启动时间（私有方法，仅供本类使用）
     */
    private static final long START_TIME = System.currentTimeMillis();

    private long getStartTime() {
        return START_TIME;
    }

    /**
     * 搜索小说书籍
     *
     * @param request 搜索请求参数（包含关键词、规则配置等）
     * @return 匹配的书籍列表
     */
    @PostMapping("/search")
    public R<List<NovelBook>> searchBooks(@RequestBody NovelCrawlerDTO.SearchRequest request) {
        log.info("开始搜索书籍，关键词: {}", request.getKeyword());
        try {
            // 1. 参数校验
            validator.validateSearchRequest(request);
            // 2. 调用业务服务
            List<NovelBook> books = novelCrawlerService.searchBooks(request.getConfig(), request.getKeyword());
            log.info("搜索书籍成功，找到 {} 本书", books != null ? books.size() : 0);
            // 3. 返回结果
            return R.ok(books);
        } catch (IllegalArgumentException e) {
            log.warn("搜索书籍参数错误: {}", e.getMessage());
            return R.fail("参数错误: " + e.getMessage());
        }
    }

    /**
     * 获取书籍详情
     *
     * @param request 书籍详情请求参数
     * @return 书籍详情对象
     */
    @PostMapping("/book/info")
    public R<NovelBook> getBookInfo(@RequestBody NovelCrawlerDTO.BookInfoRequest request) {
        log.info("开始获取书籍详情，URL: {}", request.getBookUrl());
        try {
            validator.validateBookInfoRequest(request);
            NovelBook book = novelCrawlerService.getBookInfo(request.getConfig(), request.getBookUrl());
            log.info("获取书籍详情成功，书名: {}", book != null ? book.getName() : "未知");
            return R.ok(book);
        } catch (IllegalArgumentException e) {
            log.warn("获取书籍详情参数错误: {}", e.getMessage());
            return R.fail("参数错误: " + e.getMessage());
        }
    }

    /**
     * 获取章节列表
     *
     * @param request 章节列表请求参数
     * @return 章节列表
     */
    @PostMapping("/chapter/list")
    public R<List<NovelBook.NovelChapter>> getChapterList(@RequestBody NovelCrawlerDTO.ChapterListRequest request) {
        log.info("开始获取章节列表，URL: {}", request.getChapterListUrl());
        try {
            validator.validateChapterListRequest(request);
            List<NovelBook.NovelChapter> chapters = novelCrawlerService.getChapterList(request.getConfig(), request.getChapterListUrl());
            log.info("获取章节列表成功，章节数: {}", chapters != null ? chapters.size() : 0);
            return R.ok(chapters);
        } catch (IllegalArgumentException e) {
            log.warn("获取章节列表参数错误: {}", e.getMessage());
            return R.fail("参数错误: " + e.getMessage());
        }
    }

    /**
     * 获取章节内容
     *
     * @param request 章节内容请求参数
     * @return 章节内容对象
     */
    @PostMapping("/chapter/content")
    public R<NovelBook.NovelChapter> getChapterContent(@RequestBody NovelCrawlerDTO.ChapterContentRequest request) {
        log.info("开始获取章节内容，URL: {}", request.getChapterUrl());
        try {
            validator.validateChapterContentRequest(request);
            NovelBook.NovelChapter chapter = novelCrawlerService.getChapterContent(request.getConfig(), request.getChapterUrl());
            log.info("获取章节内容成功，章节标题: {}", chapter != null ? chapter.getTitle() : "未知");
            return R.ok(chapter);
        } catch (IllegalArgumentException e) {
            log.warn("获取章节内容参数错误: {}", e.getMessage());
            return R.fail("参数错误: " + e.getMessage());
        }
    }

    /**
     * 批量获取章节内容
     *
     * @param request 批量章节内容请求参数
     * @return 章节内容对象列表
     */
    @PostMapping("/chapter/batch-content")
    public R<List<NovelBook.NovelChapter>> getBatchChapterContent(@RequestBody NovelCrawlerDTO.BatchChapterContentRequest request) {
        log.info("开始批量获取章节内容，章节数: {}", request.getChapterUrls() != null ? request.getChapterUrls().size() : 0);
        try {
            validator.validateBatchChapterContentRequest(request);
            List<NovelBook.NovelChapter> chapters = novelCrawlerService.getBatchChapterContent(request.getConfig(), request.getChapterUrls());
            log.info("批量获取章节内容成功，成功获取章节数: {}", chapters != null ? chapters.size() : 0);
            return R.ok(chapters);
        } catch (IllegalArgumentException e) {
            log.warn("批量获取章节内容参数错误: {}", e.getMessage());
            return R.fail("参数错误: " + e.getMessage());
        }
    }

    /**
     * 获取爬虫状态
     *
     * @return 爬虫状态响应
     */
    @GetMapping("/status")
    public R<NovelCrawlerDTO.CrawlerStatusResponse> getCrawlerStatus() {
        log.info("获取爬虫状态");
        NovelCrawlerDTO.CrawlerStatusResponse status = NovelCrawlerDTO.CrawlerStatusResponse.builder()
                .status("RUNNING")
                .timestamp(System.currentTimeMillis())
                .version("1.0.0")
                .uptime(System.currentTimeMillis() - getStartTime())
                .build();
        log.info("获取爬虫状态成功");
        return R.ok(status);
    }

}