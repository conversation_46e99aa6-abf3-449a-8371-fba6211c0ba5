<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shanhai</groupId>
        <artifactId>shanhai-service</artifactId>
        <version>1.0.1</version>
    </parent>

    <artifactId>shanhai-service-rule</artifactId>
    <packaging>jar</packaging>
    <description>rule服务模块</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 如需爬虫功能再加 -->
        <dependency>
            <groupId>com.shanhai</groupId>
            <artifactId>shanhai-common-crawler</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>