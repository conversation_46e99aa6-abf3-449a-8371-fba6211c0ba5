<template>
  <el-form :inline="true" :model="searchForm" class="search-form" @submit.native.prevent="$emit('search')">
    <el-form-item label="用户名">
      <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable @keyup.enter.native="$emit('search')" size="mini" />
    </el-form-item>
    <el-form-item label="状态">
      <el-select v-model="searchForm.status" placeholder="请选择状态" clearable size="mini">
        <el-option label="启用" value="1" />
        <el-option label="禁用" value="0" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="!searchCollapse" label="邮箱">
      <el-input v-model="searchForm.email" placeholder="请输入邮箱" clearable size="mini" />
    </el-form-item>
    <el-form-item v-if="!searchCollapse" label="手机号">
      <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable size="mini" />
    </el-form-item>
    <el-form-item class="search-btns">
      <el-button type="primary" size="mini" @click="$emit('search')">搜索</el-button>
      <el-button size="mini" @click="$emit('reset')">重置</el-button>
      <el-button type="text" size="mini" @click="$emit('toggle-collapse')">
        {{ searchCollapse ? '展开' : '收起' }}
        <i :class="searchCollapse ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
      </el-button>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  name: 'UserSearchForm',
  props: {
    searchForm: { type: Object, required: true },
    searchCollapse: { type: Boolean, default: true }
  }
}
</script>
<style scoped>
.search-form {
  background: #fff;
  border-radius: 8px;
  padding: 10px 16px 6px 16px;
  margin-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 8px;
  box-shadow: none;
}
.search-btns {
  margin-left: 8px;
}
</style> 