# 山海管理系统前端

基于 Vue2 + Element-UI 的后台管理系统

## 技术栈

- Vue 2.6.14
- Vue Router 3.5.3
- Vuex 3.6.2
- Element-UI 2.15.13
- Axios 0.27.2

## 项目结构

```
shanhai-ui/
├── public/                 # 静态资源
│   └── index.html         # HTML模板
├── src/                   # 源代码
│   ├── components/        # 公共组件
│   │   └── Layout.vue     # 布局组件
│   ├── views/             # 页面组件
│   │   ├── Login.vue      # 登录页
│   │   ├── Dashboard.vue  # 仪表盘
│   │   ├── content/       # 内容管理模块
│   │   │   ├── Novel.vue  # 小说管理
│   │   │   └── Rule.vue   # 规则管理
│   │   └── system/        # 系统管理模块
│   │       ├── User.vue   # 用户管理
│   │       ├── Role.vue   # 角色管理
│   │       └── Menu.vue   # 菜单管理
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── store/             # Vuex状态管理
│   │   └── index.js
│   ├── utils/             # 工具类
│   │   └── request.js     # HTTP请求
│   ├── assets/            # 资源文件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── env/                   # 环境配置文件
│   ├── development.env    # 开发环境配置
│   ├── test.env           # 测试环境配置
│   └── production.env     # 生产环境配置
├── package.json           # 依赖配置
├── vue.config.js          # Vue配置
├── start.bat              # 开发环境启动脚本
├── start-dev.bat          # 测试环境启动脚本
└── README.md              # 项目说明
```

## 安装依赖

```bash
npm install
```

## 环境配置

项目支持多环境配置：

### 开发环境
```bash
npm run serve
# 或使用启动脚本
start.bat
```
- 访问地址: http://localhost:8080
- API代理: http://localhost:8081
- 环境标识: development

### 测试环境
```bash
npm run dev
# 或使用启动脚本
start-dev.bat
```
- 访问地址: http://localhost:8082
- API代理: http://localhost:8083
- 环境标识: test

### 生产环境
```bash
npm run build
npm run build:test    # 测试环境构建
npm run build:prod    # 生产环境构建
```

## 构建命令

```bash
# 开发环境构建
npm run build

# 测试环境构建
npm run build:test

# 生产环境构建
npm run build:prod
```

## 代码检查

```bash
npm run lint
```

## 功能特性

- 用户登录/退出
- 响应式布局
- 路由守卫
- 状态管理
- HTTP请求封装
- 权限控制
- 内容管理模块
  - 小说管理
  - 规则管理
- 系统管理模块
  - 用户管理
  - 角色管理
  - 菜单管理

## 默认账号

- 用户名：admin
- 密码：123456

## 开发说明

1. 项目已配置代理，API请求会转发到 `http://localhost:8081`
2. 登录状态通过 localStorage 存储
3. 路由守卫会自动检查登录状态
4. 所有页面组件都支持懒加载

## 后续开发

- 完善各功能模块
- 添加更多页面组件
- 集成后端API
- 优化用户体验 