package com.shanhai.common.crawler.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.config.CrawlerRuleReplace;
import com.shanhai.common.crawler.repository.ReplaceRuleMapper;
import com.shanhai.common.crawler.service.ReplaceRuleService;
import org.springframework.stereotype.Service;

/**
 * 内容替换规则Service实现
 */
@Service
public class ReplaceRuleServiceImpl extends ServiceImpl<ReplaceRuleMapper, CrawlerRuleReplace> implements ReplaceRuleService {
} 