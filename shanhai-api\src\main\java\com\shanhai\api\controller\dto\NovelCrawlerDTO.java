package com.shanhai.api.controller.dto;

import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.utils.CrawlerCoreManager;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 小说爬虫相关 DTO 类
 * 使用 Lombok 简化代码，提供更简洁的 DTO 定义
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class NovelCrawlerDTO {

    /**
     * 搜索请求 DTO
     * 用于接收小说搜索请求参数
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SearchRequest {
        /**
         * 爬虫配置规则
         */
        private CrawlerRuleNovel config;
        
        /**
         * 搜索关键词
         */
        private String keyword;
    }

    /**
     * 书籍详情请求 DTO
     * 用于接收获取书籍详情请求参数
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BookInfoRequest {
        /**
         * 爬虫配置规则
         */
        private CrawlerRuleNovel config;
        
        /**
         * 书籍详情页面URL
         */
        private String bookUrl;
    }

    /**
     * 章节列表请求 DTO
     * 用于接收获取章节列表请求参数
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChapterListRequest {
        /**
         * 爬虫配置规则
         */
        private CrawlerRuleNovel config;
        
        /**
         * 章节列表页面URL
         */
        private String chapterListUrl;
    }

    /**
     * 章节内容请求 DTO
     * 用于接收获取章节内容请求参数
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChapterContentRequest {
        /**
         * 爬虫配置规则
         */
        private CrawlerRuleNovel config;
        
        /**
         * 章节内容页面URL
         */
        private String chapterUrl;
    }

    /**
     * 批量章节内容请求 DTO
     * 用于接收批量获取章节内容请求参数
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BatchChapterContentRequest {
        /**
         * 爬虫配置规则
         */
        private CrawlerRuleNovel config;
        
        /**
         * 章节URL列表
         */
        private List<String> chapterUrls;
    }

    /**
     * 爬虫状态响应 DTO
     * 用于返回爬虫服务的运行状态信息
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CrawlerStatusResponse {
        /**
         * 爬虫状态
         */
        private String status;
        
        /**
         * 时间戳
         */
        private Long timestamp;
        
        /**
         * 版本号
         */
        private String version;
        
        /**
         * 运行时间（毫秒）
         */
        private Long uptime;
    }

    /**
     * 分页章节列表响应 DTO
     * 用于返回分页的章节列表信息
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PagedChapterListResponse {
        /**
         * 章节列表
         */
        private List<NovelBook.NovelChapter> chapters;
        
        /**
         * 总章节数
         */
        private Integer total;
        
        /**
         * 当前页码
         */
        private Integer page;
        
        /**
         * 每页大小
         */
        private Integer size;
        
        /**
         * 总页数
         */
        private Integer totalPages;
    }

    /**
     * 批量章节内容响应 DTO
     * 用于返回批量获取章节内容的详细信息
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BatchChapterContentResponse {
        /**
         * 章节列表
         */
        private List<NovelBook.NovelChapter> chapters;
        
        /**
         * 总章节数
         */
        private Integer totalCount;
        
        /**
         * 成功获取的章节数
         */
        private Long successCount;
        
        /**
         * 失败的章节数
         */
        private Long failedCount;
        
        /**
         * 成功率
         */
        private Double successRate;
    }

    /**
     * 爬虫状态和监控指标响应 DTO
     * 用于返回爬虫服务的状态和详细的监控指标
     * 
     * <AUTHOR>
     * @since 2024-01-01
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CrawlerStatusWithMetricsResponse {
        /**
         * 基础状态信息
         */
        private CrawlerStatusResponse status;
        
        /**
         * 监控指标
         */
        private CrawlerCoreManager.PerformanceStats metrics;
        
        /**
         * 线程池统计信息
         */
        private CrawlerCoreManager.CacheStats cacheStats;
    }
} 