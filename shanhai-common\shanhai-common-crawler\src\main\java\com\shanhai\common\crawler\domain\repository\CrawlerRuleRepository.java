package com.shanhai.common.crawler.domain.repository;

import com.shanhai.common.crawler.domain.aggregate.CrawlerRule;
import com.shanhai.common.crawler.domain.valueobject.CrawlerMode;
import com.shanhai.common.crawler.domain.valueobject.RuleStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 爬虫规则仓储接口
 * <p>
 * 定义爬虫规则聚合根的持久化操作接口。
 * 作为领域层的仓储接口，只定义业务相关的查询方法，不包含技术实现细节。
 * 
 * <p>主要职责：
 * <ul>
 *   <li>提供爬虫规则的基本CRUD操作</li>
 *   <li>支持基于业务条件的查询</li>
 *   <li>提供聚合根的完整加载和保存</li>
 *   <li>确保数据的一致性和完整性</li>
 * </ul>
 * 
 * <p>设计原则：
 * <ul>
 *   <li>面向业务需求设计查询方法</li>
 *   <li>返回完整的聚合根对象</li>
 *   <li>隐藏底层存储技术细节</li>
 *   <li>支持事务边界控制</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-01-01
 */
public interface CrawlerRuleRepository {

    // ==================== 基本CRUD操作 ====================

    /**
     * 保存爬虫规则
     * 如果规则不存在则创建，如果存在则更新
     * 
     * @param rule 爬虫规则聚合根
     * @return 保存后的规则
     */
    CrawlerRule save(CrawlerRule rule);

    /**
     * 根据ID查找爬虫规则
     * 返回完整的聚合根，包含所有子配置
     * 
     * @param id 规则ID
     * @return 爬虫规则，如果不存在返回空
     */
    Optional<CrawlerRule> findById(String id);

    /**
     * 查找所有爬虫规则
     * 返回完整的聚合根列表
     * 
     * @return 所有爬虫规则
     */
    List<CrawlerRule> findAll();

    /**
     * 根据ID删除爬虫规则
     * 会级联删除所有相关的子配置
     * 
     * @param id 规则ID
     */
    void deleteById(String id);

    /**
     * 检查规则是否存在
     * 
     * @param id 规则ID
     * @return 是否存在
     */
    boolean existsById(String id);

    // ==================== 业务查询方法 ====================

    /**
     * 根据数据源标识符查找爬虫规则
     * 数据源标识符在系统中应该是唯一的
     * 
     * @param sourceIdentifier 数据源标识符
     * @return 爬虫规则，如果不存在返回空
     */
    Optional<CrawlerRule> findBySourceIdentifier(String sourceIdentifier);

    /**
     * 根据数据源名称模糊查找爬虫规则
     * 支持部分匹配查询
     * 
     * @param sourceName 数据源名称关键词
     * @return 匹配的规则列表
     */
    List<CrawlerRule> findBySourceNameLike(String sourceName);

    /**
     * 根据规则状态查找爬虫规则
     * 
     * @param status 规则状态
     * @return 指定状态的规则列表
     */
    List<CrawlerRule> findByStatus(RuleStatus status);

    /**
     * 根据采集模式查找爬虫规则
     * 
     * @param mode 采集模式
     * @return 指定模式的规则列表
     */
    List<CrawlerRule> findByMode(CrawlerMode mode);

    /**
     * 查找启用状态的爬虫规则
     * 等同于 findByStatus(RuleStatus.ENABLED)
     * 
     * @return 启用的规则列表
     */
    List<CrawlerRule> findEnabledRules();

    /**
     * 查找禁用状态的爬虫规则
     * 等同于 findByStatus(RuleStatus.DISABLED)
     * 
     * @return 禁用的规则列表
     */
    List<CrawlerRule> findDisabledRules();

    /**
     * 查找测试状态的爬虫规则
     * 等同于 findByStatus(RuleStatus.TESTING)
     * 
     * @return 测试状态的规则列表
     */
    List<CrawlerRule> findTestingRules();

    // ==================== 高级查询方法 ====================

    /**
     * 根据多个条件查找爬虫规则
     * 
     * @param status 规则状态（可选）
     * @param mode 采集模式（可选）
     * @param sourceNameKeyword 数据源名称关键词（可选）
     * @return 匹配条件的规则列表
     */
    List<CrawlerRule> findByConditions(RuleStatus status, CrawlerMode mode, String sourceNameKeyword);

    /**
     * 根据创建时间范围查找爬虫规则
     * 
     * @param startTime 开始时间（包含）
     * @param endTime 结束时间（包含）
     * @return 指定时间范围内创建的规则列表
     */
    List<CrawlerRule> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据更新时间范围查找爬虫规则
     * 
     * @param startTime 开始时间（包含）
     * @param endTime 结束时间（包含）
     * @return 指定时间范围内更新的规则列表
     */
    List<CrawlerRule> findByUpdateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找最近创建的爬虫规则
     * 
     * @param limit 返回数量限制
     * @return 最近创建的规则列表，按创建时间倒序
     */
    List<CrawlerRule> findRecentlyCreated(int limit);

    /**
     * 查找最近更新的爬虫规则
     * 
     * @param limit 返回数量限制
     * @return 最近更新的规则列表，按更新时间倒序
     */
    List<CrawlerRule> findRecentlyUpdated(int limit);

    // ==================== 统计查询方法 ====================

    /**
     * 统计爬虫规则总数
     * 
     * @return 规则总数
     */
    long count();

    /**
     * 根据状态统计爬虫规则数量
     * 
     * @param status 规则状态
     * @return 指定状态的规则数量
     */
    long countByStatus(RuleStatus status);

    /**
     * 根据采集模式统计爬虫规则数量
     * 
     * @param mode 采集模式
     * @return 指定模式的规则数量
     */
    long countByMode(CrawlerMode mode);

    /**
     * 统计启用状态的规则数量
     * 
     * @return 启用规则数量
     */
    long countEnabledRules();

    /**
     * 统计各状态的规则数量
     * 
     * @return 状态统计映射（状态 -> 数量）
     */
    java.util.Map<RuleStatus, Long> countByStatusGrouped();

    /**
     * 统计各采集模式的规则数量
     * 
     * @return 模式统计映射（模式 -> 数量）
     */
    java.util.Map<CrawlerMode, Long> countByModeGrouped();

    // ==================== 批量操作方法 ====================

    /**
     * 批量保存爬虫规则
     * 
     * @param rules 规则列表
     * @return 保存后的规则列表
     */
    List<CrawlerRule> saveAll(List<CrawlerRule> rules);

    /**
     * 批量删除爬虫规则
     * 
     * @param ids 规则ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 批量更新规则状态
     * 
     * @param ids 规则ID列表
     * @param newStatus 新状态
     * @return 更新的规则数量
     */
    int updateStatusByIds(List<String> ids, RuleStatus newStatus);

    /**
     * 批量启用规则
     * 
     * @param ids 规则ID列表
     * @return 更新的规则数量
     */
    int enableRulesByIds(List<String> ids);

    /**
     * 批量禁用规则
     * 
     * @param ids 规则ID列表
     * @return 更新的规则数量
     */
    int disableRulesByIds(List<String> ids);

    // ==================== 验证和检查方法 ====================

    /**
     * 检查数据源标识符是否已存在
     * 
     * @param sourceIdentifier 数据源标识符
     * @return 是否已存在
     */
    boolean existsBySourceIdentifier(String sourceIdentifier);

    /**
     * 检查数据源标识符是否已被其他规则使用
     * 
     * @param sourceIdentifier 数据源标识符
     * @param excludeRuleId 排除的规则ID
     * @return 是否已被其他规则使用
     */
    boolean existsBySourceIdentifierAndIdNot(String sourceIdentifier, String excludeRuleId);

    /**
     * 查找具有相同基础URL的规则
     * 用于检测可能的重复配置
     * 
     * @param baseUrl 基础URL
     * @return 具有相同基础URL的规则列表
     */
    List<CrawlerRule> findBySourceBaseUrl(String baseUrl);

    /**
     * 查找可能重复的规则
     * 基于数据源标识符、名称、URL等进行重复检测
     * 
     * @param rule 待检查的规则
     * @return 可能重复的规则列表
     */
    List<CrawlerRule> findPossibleDuplicates(CrawlerRule rule);

    // ==================== 分页查询方法 ====================

    /**
     * 分页查找爬虫规则
     * 
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页结果
     */
    PageResult<CrawlerRule> findWithPagination(int page, int size);

    /**
     * 根据条件分页查找爬虫规则
     * 
     * @param status 规则状态（可选）
     * @param mode 采集模式（可选）
     * @param sourceNameKeyword 数据源名称关键词（可选）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页结果
     */
    PageResult<CrawlerRule> findByConditionsWithPagination(
            RuleStatus status, CrawlerMode mode, String sourceNameKeyword, int page, int size);

    /**
     * 分页结果包装类
     * 
     * @param <T> 数据类型
     */
    class PageResult<T> {
        private final List<T> content;
        private final long totalElements;
        private final int totalPages;
        private final int currentPage;
        private final int pageSize;
        private final boolean hasNext;
        private final boolean hasPrevious;

        public PageResult(List<T> content, long totalElements, int currentPage, int pageSize) {
            this.content = content;
            this.totalElements = totalElements;
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.totalPages = (int) Math.ceil((double) totalElements / pageSize);
            this.hasNext = currentPage < totalPages - 1;
            this.hasPrevious = currentPage > 0;
        }

        // Getters
        public List<T> getContent() { return content; }
        public long getTotalElements() { return totalElements; }
        public int getTotalPages() { return totalPages; }
        public int getCurrentPage() { return currentPage; }
        public int getPageSize() { return pageSize; }
        public boolean isHasNext() { return hasNext; }
        public boolean isHasPrevious() { return hasPrevious; }
        public boolean isEmpty() { return content.isEmpty(); }
        public int getNumberOfElements() { return content.size(); }
    }
}
