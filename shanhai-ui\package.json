{"name": "shan<PERSON>-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "set PORT=8082 && vue-cli-service serve", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.27.2", "element-ui": "^2.15.13", "vue": "^2.6.14", "vue-router": "^3.5.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}