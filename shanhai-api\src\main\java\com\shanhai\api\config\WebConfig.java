package com.shanhai.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * Web配置类
 * <p>
 * 配置Web相关的功能，包括：
 * <ul>
 *   <li>跨域配置 (CORS)</li>
 *   <li>静态资源处理</li>
 *   <li>拦截器配置</li>
 *   <li>消息转换器配置</li>
 * </ul>
 * 
 * <p>设计原则：
 * <ul>
 *   <li>安全优先：合理配置跨域和安全策略</li>
 *   <li>性能优化：静态资源缓存和压缩</li>
 *   <li>开发友好：开发环境宽松配置</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024-01-01
 */
@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${cors.allowed-origins:http://localhost:3000,http://localhost:8080}")
    private String[] allowedOrigins;

    @Value("${cors.max-age:3600}")
    private Long corsMaxAge;

    /**
     * 跨域配置
     * 根据不同环境配置不同的跨域策略
     * 
     * @return CORS配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 根据环境配置允许的源地址
        if ("dev".equals(activeProfile) || "test".equals(activeProfile)) {
            // 开发和测试环境：宽松配置
            configuration.setAllowedOriginPatterns(Arrays.asList(
                "http://localhost:*",
                "http://127.0.0.1:*",
                "https://localhost:*",
                "https://127.0.0.1:*"
            ));
        } else {
            // 生产环境：严格配置
            configuration.setAllowedOrigins(Arrays.asList(allowedOrigins));
        }
        
        // 允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"
        ));
        
        // 允许的请求头
        configuration.setAllowedHeaders(Arrays.asList(
            "Origin", "Content-Type", "Accept", "Authorization", 
            "X-Requested-With", "X-Token", "Cache-Control"
        ));
        
        // 暴露的响应头
        configuration.setExposedHeaders(Arrays.asList(
            "Content-Length", "Access-Control-Allow-Origin",
            "Access-Control-Allow-Headers", "Cache-Control", 
            "Content-Language", "Content-Type"
        ));
        
        // 允许携带认证信息
        configuration.setAllowCredentials(true);
        
        // 预检请求的缓存时间（秒）
        configuration.setMaxAge(corsMaxAge);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        log.info("CORS配置已加载，环境: {}, 最大缓存时间: {}秒", activeProfile, corsMaxAge);
        
        return source;
    }

    /**
     * 静态资源处理配置
     * 配置静态资源的访问路径和缓存策略
     * 
     * @param registry 资源处理器注册表
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600); // 缓存1小时

        // 配置上传文件访问路径
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:uploads/")
                .setCachePeriod(86400); // 缓存24小时

        // 配置API文档资源
        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        log.debug("静态资源处理器配置完成");
    }

    /**
     * 拦截器配置
     * 添加自定义拦截器
     * 
     * @param registry 拦截器注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 可以在这里添加自定义拦截器
        // registry.addInterceptor(new LoggingInterceptor())
        //         .addPathPatterns("/**")
        //         .excludePathPatterns("/static/**", "/error");
        
        log.debug("拦截器配置完成");
    }
}
